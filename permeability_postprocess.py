#!/usr/bin/env python3
"""
reactiveTransportDBSFoam 渗透率后处理程序
基于现有eps数据计算渗透率并生成ParaView文件
"""

import os
import sys
import numpy as np
import subprocess
from pathlib import Path

class PermeabilityPostProcessor:
    def __init__(self, case_dir="."):
        self.case_dir = Path(case_dir).resolve()
        self.kf = 1.8e12  # Kozeny-Carman常数
        self.time_dirs = []
        self.total_permeability_data = {}
        
    def find_time_directories(self):
        """查找所有时间步目录"""
        print("🔍 查找时间步目录...")
        
        for item in self.case_dir.iterdir():
            if item.is_dir():
                try:
                    float(item.name)
                    if (item / "eps").exists():
                        self.time_dirs.append(item.name)
                except ValueError:
                    continue
        
        self.time_dirs.sort(key=float)
        print(f"📅 找到 {len(self.time_dirs)} 个时间步: {self.time_dirs[0]} → {self.time_dirs[-1]}")
        return len(self.time_dirs) > 0
    
    def reconstruct_parallel_data(self):
        """重构并行数据"""
        if (self.case_dir / "processor0").exists():
            print("📦 重构并行数据...")
            try:
                subprocess.run(["reconstructPar"], cwd=self.case_dir, 
                             capture_output=True, check=True)
                print("✅ 并行数据重构成功")
            except subprocess.CalledProcessError:
                print("⚠️  并行数据重构失败，继续处理...")
    
    def create_permeability_config(self):
        """创建渗透率计算配置"""
        print("⚙️  创建渗透率计算配置...")
        
        system_dir = self.case_dir / "system"
        system_dir.mkdir(exist_ok=True)
        
        config_content = f"""expressions
{{
    type            expressions;
    libs            ("libutilityFunctionObjects.so");
    
    expressions
    (
        {{
            name        K;
            expression  "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/{self.kf}";
            dimensions  [0 2 0 0 0 0 0];
        }}
        {{
            name        Kinv;
            expression  "{self.kf}*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)";
            dimensions  [0 -2 0 0 0 0 0];
        }}
    );
    
    writeControl    writeTime;
}}"""
        
        with open(system_dir / "expressions", 'w') as f:
            f.write(config_content)
        
        print(f"✅ 配置完成 (kf = {self.kf:.2e})")
    
    def calculate_permeability_fields(self):
        """为所有时间步计算渗透率字段"""
        print("🧮 计算渗透率字段...")
        
        processed = 0
        failed = 0
        
        for time_dir in self.time_dirs:
            time_path = self.case_dir / time_dir
            
            # 检查是否已有渗透率字段
            if (time_path / "K").exists() and (time_path / "Kinv").exists():
                print(f"✅ 时间步 {time_dir} 已有渗透率字段")
                processed += 1
                continue
            
            print(f"🔄 处理时间步 {time_dir}...")
            
            try:
                # 使用postProcess计算渗透率
                result = subprocess.run(
                    ["postProcess", "-func", "expressions", "-time", time_dir],
                    cwd=self.case_dir,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0 and (time_path / "K").exists():
                    processed += 1
                    print(f"✅ 时间步 {time_dir} 成功")
                else:
                    failed += 1
                    print(f"❌ 时间步 {time_dir} 失败")
                    
            except Exception as e:
                failed += 1
                print(f"❌ 时间步 {time_dir} 错误: {e}")
        
        print(f"📊 渗透率计算完成: {processed} 成功, {failed} 失败")
        return processed > 0
    
    def calculate_total_permeability(self):
        """计算总渗透率"""
        print("📈 计算总渗透率...")
        
        for time_dir in self.time_dirs:
            time_path = self.case_dir / time_dir
            
            if not (time_path / "K").exists():
                continue
            
            print(f"📊 分析时间步 {time_dir}...")
            
            try:
                # 读取K字段文件
                k_file = time_path / "K"
                with open(k_file, 'r') as f:
                    lines = f.readlines()
                
                # 解析字段数据
                k_values = self._parse_field_data(lines)
                
                if k_values is not None:
                    # 计算统计数据
                    k_min = np.min(k_values)
                    k_max = np.max(k_values)
                    k_mean = np.mean(k_values)
                    k_std = np.std(k_values)
                    
                    # 计算几何平均 (更适合渗透率)
                    k_values_positive = k_values[k_values > 0]
                    if len(k_values_positive) > 0:
                        k_geomean = np.exp(np.mean(np.log(k_values_positive)))
                    else:
                        k_geomean = 0
                    
                    self.total_permeability_data[time_dir] = {
                        'min': k_min,
                        'max': k_max,
                        'mean': k_mean,
                        'geomean': k_geomean,
                        'std': k_std,
                        'cells': len(k_values)
                    }
                    
                    print(f"   K范围: {k_min:.2e} - {k_max:.2e} m²")
                    print(f"   K平均: {k_mean:.2e} m² (算术), {k_geomean:.2e} m² (几何)")
                
            except Exception as e:
                print(f"⚠️  时间步 {time_dir} 分析失败: {e}")
    
    def _parse_field_data(self, lines):
        """解析OpenFOAM字段数据"""
        try:
            # 查找数据开始位置
            data_start = -1
            is_uniform = False
            
            for i, line in enumerate(lines):
                if "internalField" in line:
                    if "uniform" in line:
                        # uniform字段
                        is_uniform = True
                        value = float(line.split()[-1].rstrip(';'))
                        return np.array([value])  # 返回单个值
                    elif "nonuniform" in line:
                        # nonuniform字段
                        data_start = i + 2  # 跳过数量行
                        break
            
            if data_start == -1:
                return None
            
            # 读取数据数量
            num_cells = int(lines[data_start - 1].strip())
            
            # 读取数据
            values = []
            for i in range(data_start, min(data_start + num_cells, len(lines))):
                line = lines[i].strip()
                if line and not line.startswith('(') and not line.startswith(')'):
                    try:
                        values.append(float(line))
                    except ValueError:
                        continue
            
            return np.array(values) if values else None
            
        except Exception:
            return None
    
    def generate_paraview_files(self):
        """生成ParaView文件"""
        print("🎨 生成ParaView文件...")
        
        # 清理旧文件
        for foam_file in self.case_dir.glob("*.foam"):
            foam_file.unlink()
        for openfoam_file in self.case_dir.glob("*.OpenFOAM"):
            openfoam_file.unlink()
        
        vtk_dir = self.case_dir / "VTK"
        if vtk_dir.exists():
            import shutil
            shutil.rmtree(vtk_dir)
        
        # 创建新的.foam文件
        foam_file = self.case_dir / "permeability_analysis.foam"
        foam_file.touch()
        print(f"✅ 创建 {foam_file.name}")
        
        # 转换为VTK格式
        print("🔄 转换为VTK格式...")
        try:
            result = subprocess.run(
                ["foamToVTK"],
                cwd=self.case_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and vtk_dir.exists():
                print("✅ VTK文件生成成功")
                return True
            else:
                print("⚠️  VTK转换可能有问题")
                return False
                
        except Exception as e:
            print(f"❌ VTK转换失败: {e}")
            return False
    
    def generate_report(self):
        """生成分析报告"""
        print("📝 生成分析报告...")
        
        report_file = self.case_dir / "PERMEABILITY_ANALYSIS.txt"
        
        with open(report_file, 'w') as f:
            f.write("reactiveTransportDBSFoam 渗透率分析报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"分析时间: {subprocess.run(['date'], capture_output=True, text=True).stdout.strip()}\n")
            f.write(f"算例目录: {self.case_dir}\n")
            f.write(f"时间步数量: {len(self.time_dirs)}\n")
            f.write(f"时间范围: {self.time_dirs[0]} - {self.time_dirs[-1]}\n\n")
            
            f.write("渗透率计算公式:\n")
            f.write(f"K = eps³/((1-eps)²×{self.kf:.2e})\n")
            f.write("基于Kozeny-Carman方程\n\n")
            
            f.write("各时间步渗透率统计:\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'时间步':<10} {'最小值(m²)':<15} {'最大值(m²)':<15} {'算术平均(m²)':<15} {'几何平均(m²)':<15}\n")
            f.write("-" * 80 + "\n")
            
            for time_dir in self.time_dirs:
                if time_dir in self.total_permeability_data:
                    data = self.total_permeability_data[time_dir]
                    f.write(f"{time_dir:<10} {data['min']:<15.2e} {data['max']:<15.2e} "
                           f"{data['mean']:<15.2e} {data['geomean']:<15.2e}\n")
            
            f.write("\n总体渗透率演化:\n")
            f.write("-" * 40 + "\n")
            
            if self.total_permeability_data:
                times = [float(t) for t in self.total_permeability_data.keys()]
                means = [self.total_permeability_data[str(int(t))]['mean'] for t in sorted(times)]
                geomeans = [self.total_permeability_data[str(int(t))]['geomean'] for t in sorted(times)]
                
                f.write(f"初始平均渗透率: {means[0]:.2e} m² (算术), {geomeans[0]:.2e} m² (几何)\n")
                f.write(f"最终平均渗透率: {means[-1]:.2e} m² (算术), {geomeans[-1]:.2e} m² (几何)\n")
                f.write(f"渗透率变化倍数: {means[-1]/means[0]:.2f} (算术), {geomeans[-1]/geomeans[0]:.2f} (几何)\n")
            
            f.write("\nParaView可视化:\n")
            f.write("-" * 20 + "\n")
            f.write("1. VTK文件 (推荐): paraview VTK/*.vtm.series\n")
            f.write("2. OpenFOAM文件: paraview permeability_analysis.foam\n")
            f.write("\n可用字段:\n")
            f.write("- eps: 孔隙度\n")
            f.write("- K: 渗透率\n")
            f.write("- Kinv: 渗透率倒数\n")
            f.write("- 其他原有字段\n")
        
        print(f"✅ 报告保存到 {report_file.name}")
    
    def run(self):
        """运行完整的后处理流程"""
        print("🚀 reactiveTransportDBSFoam 渗透率后处理")
        print("=" * 50)
        
        # 1. 重构并行数据
        self.reconstruct_parallel_data()
        
        # 2. 查找时间步
        if not self.find_time_directories():
            print("❌ 没有找到有效的时间步数据")
            return False
        
        # 3. 创建配置
        self.create_permeability_config()
        
        # 4. 计算渗透率字段
        if not self.calculate_permeability_fields():
            print("❌ 渗透率字段计算失败")
            return False
        
        # 5. 计算总渗透率
        self.calculate_total_permeability()
        
        # 6. 生成ParaView文件
        self.generate_paraview_files()
        
        # 7. 生成报告
        self.generate_report()
        
        print("\n🎉 后处理完成!")
        print(f"📊 处理了 {len(self.time_dirs)} 个时间步")
        print(f"📈 计算了 {len(self.total_permeability_data)} 个时间步的总渗透率")
        
        print("\n🚀 查看结果:")
        print(f"   paraview {self.case_dir}/VTK/*.vtm.series")
        print(f"   cat {self.case_dir}/PERMEABILITY_ANALYSIS.txt")
        
        return True

def main():
    if len(sys.argv) > 1:
        case_dir = sys.argv[1]
    else:
        case_dir = "."
    
    processor = PermeabilityPostProcessor(case_dir)
    success = processor.run()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
