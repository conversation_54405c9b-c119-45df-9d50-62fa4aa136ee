#!/bin/bash

# 为所有时间步创建渗透率字段
# 用法: ./fix_permeability_all_times.sh [算例目录]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 为所有时间步创建渗透率字段${NC}"
echo "======================================================="

# 设置算例目录
if [ -z "$1" ]; then
    CASE_DIR="."
else
    CASE_DIR="$1"
fi

echo -e "${YELLOW}📁 算例目录: $CASE_DIR${NC}"

# 检查算例目录
if [ ! -d "$CASE_DIR" ]; then
    echo -e "${RED}❌ 算例目录不存在: $CASE_DIR${NC}"
    exit 1
fi

cd "$CASE_DIR"

# 找到所有时间步
echo -e "${YELLOW}🔍 查找所有时间步...${NC}"
TIME_DIRS=($(find . -maxdepth 1 -name "[0-9]*" -type d | sed 's|./||' | sort -n))

if [ ${#TIME_DIRS[@]} -eq 0 ]; then
    echo -e "${RED}❌ 没有找到时间步数据${NC}"
    exit 1
fi

echo -e "${GREEN}📅 找到 ${#TIME_DIRS[@]} 个时间步: ${TIME_DIRS[0]} 到 ${TIME_DIRS[-1]}${NC}"

# 创建expressions字典
echo -e "${YELLOW}🔧 创建postProcess配置...${NC}"
mkdir -p system
cat > system/expressions << 'EOF'
expressions
{
    type            expressions;
    libs            ("libutilityFunctionObjects.so");
    
    expressions
    (
        {
            name        K;
            expression  "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/1.8e12";
            dimensions  [0 2 0 0 0 0 0];
        }
        {
            name        Kinv;
            expression  "1.8e12*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)";
            dimensions  [0 -2 0 0 0 0 0];
        }
    );
    
    writeControl    writeTime;
}
EOF

# 方法1: 尝试对所有时间步使用postProcess
echo -e "${YELLOW}🔧 方法1: 使用postProcess处理所有时间步...${NC}"

# 构建时间列表
TIME_LIST=""
for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ]; then
        TIME_LIST="$TIME_LIST$time,"
    fi
done
TIME_LIST=${TIME_LIST%,}  # 移除最后的逗号

if [ -n "$TIME_LIST" ]; then
    echo -e "${BLUE}📊 处理时间步: $TIME_LIST${NC}"
    
    if postProcess -func expressions -time "($TIME_LIST)" > postprocess.log 2>&1; then
        echo -e "${GREEN}✅ postProcess方法成功处理所有时间步${NC}"
        SUCCESS=true
    else
        echo -e "${YELLOW}⚠️  postProcess方法失败，使用逐个处理...${NC}"
        SUCCESS=false
    fi
else
    SUCCESS=false
fi

# 方法2: 逐个时间步处理
if [ "$SUCCESS" != "true" ]; then
    echo -e "${YELLOW}🔧 方法2: 逐个处理每个时间步...${NC}"
    
    PROCESSED=0
    FAILED=0
    
    for time in "${TIME_DIRS[@]}"; do
        if [ -f "$time/eps" ]; then
            echo -ne "${BLUE}📊 处理时间步 $time...${NC}\r"
            
            # 检查是否已经有K和Kinv字段
            if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
                echo -e "${GREEN}✅ 时间步 $time 已有渗透率字段${NC}"
                ((PROCESSED++))
                continue
            fi
            
            # 尝试postProcess单个时间步
            if postProcess -func expressions -time "$time" > /dev/null 2>&1; then
                if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
                    ((PROCESSED++))
                else
                    # 手动创建字段
                    create_manual_fields "$time"
                    if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
                        ((PROCESSED++))
                    else
                        ((FAILED++))
                    fi
                fi
            else
                # 手动创建字段
                create_manual_fields "$time"
                if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
                    ((PROCESSED++))
                else
                    ((FAILED++))
                fi
            fi
        else
            echo -e "${YELLOW}⚠️  时间步 $time 没有eps字段，跳过${NC}"
        fi
    done
    
    echo ""
    echo -e "${GREEN}📊 处理完成: $PROCESSED 成功, $FAILED 失败${NC}"
fi

# 函数：手动创建字段
create_manual_fields() {
    local time_dir=$1
    
    # 检查eps字段类型
    if grep -q "internalField.*uniform" "$time_dir/eps"; then
        # Uniform字段
        local eps_value=$(grep "internalField.*uniform" "$time_dir/eps" | awk '{print $3}' | sed 's/;//')
        
        if [[ "$eps_value" =~ ^[0-9]*\.?[0-9]+([eE][+-]?[0-9]+)?$ ]]; then
            # 计算渗透率值
            local k_value=$(python3 -c "
import math
eps = max(min(float('$eps_value'), 0.999999), 1e-6)
k = eps**3/((1-eps)**2*1.8e12)
print(f'{k:.12e}')
")
            local kinv_value=$(python3 -c "
import math
eps = max(min(float('$eps_value'), 0.999999), 1e-6)
kinv = 1.8e12*(1-eps)**2/eps**3
print(f'{kinv:.12e}')
")
            
            # 创建uniform K字段
            cat > "$time_dir/K" << EOF
/*--------------------------------*- C++ -*----------------------------------*\\
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "$time_dir";
    object      K;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 0 0 0 0 0];

internalField   uniform $k_value;

boundaryField
{
    #includeEtc "caseDicts/setConstraintTypes"
}

// ************************************************************************* //
EOF

            # 创建uniform Kinv字段
            cat > "$time_dir/Kinv" << EOF
/*--------------------------------*- C++ -*----------------------------------*\\
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "$time_dir";
    object      Kinv;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 -2 0 0 0 0 0];

internalField   uniform $kinv_value;

boundaryField
{
    #includeEtc "caseDicts/setConstraintTypes"
}

// ************************************************************************* //
EOF
        fi
    fi
}

# 最终检查和报告
echo ""
echo -e "${BLUE}🔍 最终检查...${NC}"

TOTAL_TIMES=0
WITH_K=0
WITH_KINV=0

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ]; then
        ((TOTAL_TIMES++))
        [ -f "$time/K" ] && ((WITH_K++))
        [ -f "$time/Kinv" ] && ((WITH_KINV++))
    fi
done

echo -e "${BLUE}📊 统计结果:${NC}"
echo -e "   总时间步数: $TOTAL_TIMES"
echo -e "   有K字段: $WITH_K"
echo -e "   有Kinv字段: $WITH_KINV"

if [ "$WITH_K" -eq "$TOTAL_TIMES" ] && [ "$WITH_KINV" -eq "$TOTAL_TIMES" ]; then
    echo ""
    echo -e "${GREEN}🎉 SUCCESS! 所有时间步都有渗透率字段${NC}"
    
    # 创建ParaView文件
    echo -e "${YELLOW}📄 创建ParaView文件...${NC}"
    rm -f *.foam *.OpenFOAM
    touch permeability_all_times.foam
    
    # 转换所有时间步为VTK
    echo -e "${YELLOW}🔄 转换所有时间步为VTK格式...${NC}"
    foamToVTK > /dev/null 2>&1 || true
    
    echo ""
    echo -e "${BLUE}🚀 在ParaView中查看所有时间步:${NC}"
    echo -e "${GREEN}   推荐: paraview VTK/*.vtm.series${NC}"
    echo -e "   备选: paraview permeability_all_times.foam"
    echo ""
    echo -e "${BLUE}📊 可用字段 (所有时间步):${NC}"
    echo -e "   ✅ K (渗透率)"
    echo -e "   ✅ Kinv (渗透率倒数)"
    echo -e "   ✅ eps (孔隙度)"
    echo ""
    echo -e "${GREEN}🎯 所有时间步的渗透率问题已解决!${NC}"
    
else
    echo ""
    echo -e "${YELLOW}⚠️  部分时间步缺少渗透率字段${NC}"
    echo -e "${YELLOW}💡 缺少的时间步可能没有有效的eps数据${NC}"
fi
