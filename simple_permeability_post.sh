#!/bin/bash

# 简化版渗透率后处理脚本
# 用法: ./simple_permeability_post.sh [算例目录]

echo "🚀 reactiveTransportDBSFoam 渗透率后处理"
echo "========================================"

CASE_DIR="${1:-.}"
echo "📁 算例目录: $CASE_DIR"

cd "$CASE_DIR"

# 1. 查找时间步
echo "🔍 查找时间步..."
TIME_DIRS=($(ls -d [0-9]* 2>/dev/null | sort -n))

if [ ${#TIME_DIRS[@]} -eq 0 ]; then
    echo "❌ 没有找到时间步数据"
    exit 1
fi

echo "✅ 找到 ${#TIME_DIRS[@]} 个时间步: ${TIME_DIRS[0]} → ${TIME_DIRS[-1]}"

# 检查eps数据
EPS_COUNT=0
for time in "${TIME_DIRS[@]}"; do
    [ -f "$time/eps" ] && ((EPS_COUNT++))
done

echo "✅ 有eps数据: $EPS_COUNT 个时间步"

# 2. 创建配置
echo "⚙️  创建配置..."
mkdir -p system

cat > system/expressions << 'EOF'
expressions
{
    type            expressions;
    libs            ("libutilityFunctionObjects.so");
    
    expressions
    (
        {
            name        K;
            expression  "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/1.8e12";
            dimensions  [0 2 0 0 0 0 0];
        }
        {
            name        Kinv;
            expression  "1.8e12*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)";
            dimensions  [0 -2 0 0 0 0 0];
        }
    );
    
    writeControl    writeTime;
}
EOF

echo "✅ 配置完成"

# 3. 计算渗透率
echo "🧮 计算渗透率..."
PROCESSED=0

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ]; then
        if [ ! -f "$time/K" ]; then
            echo "   处理时间步 $time..."
            postProcess -func expressions -time "$time" > /dev/null 2>&1
        fi
        
        if [ -f "$time/K" ]; then
            ((PROCESSED++))
            echo "   ✅ 时间步 $time 有渗透率数据"
        fi
    fi
done

echo "✅ $PROCESSED 个时间步有渗透率数据"

# 4. 分析总渗透率
echo "📈 分析总渗透率..."

cat > PERMEABILITY_SUMMARY.txt << EOF
reactiveTransportDBSFoam 渗透率分析
================================

分析时间: $(date)
算例目录: $(pwd)
时间步: ${TIME_DIRS[0]} → ${TIME_DIRS[-1]}
有渗透率数据: $PROCESSED 个时间步

各时间步渗透率:
EOF

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/K" ] && [ -f "$time/eps" ]; then
        echo "分析时间步 $time..."
        
        # 检查是否为uniform字段
        if grep -q "uniform" "$time/K" 2>/dev/null; then
            K_VALUE=$(grep "internalField.*uniform" "$time/K" | awk '{print $3}' | sed 's/;//')
            EPS_VALUE=$(grep "internalField.*uniform" "$time/eps" | awk '{print $3}' | sed 's/;//' 2>/dev/null || echo "N/A")
            
            echo "时间步 $time: K = $K_VALUE m², eps = $EPS_VALUE" >> PERMEABILITY_SUMMARY.txt
            echo "   K = $K_VALUE m², eps = $EPS_VALUE"
        else
            echo "时间步 $time: K = 非uniform字段" >> PERMEABILITY_SUMMARY.txt
            echo "   K = 非uniform字段"
        fi
    fi
done

# 5. 生成ParaView文件
echo "🎨 生成ParaView文件..."
rm -f *.foam *.OpenFOAM
touch permeability_post.foam

echo "🔄 转换VTK..."
foamToVTK > /dev/null 2>&1

if [ -d "VTK" ]; then
    echo "✅ VTK文件生成成功"
    VTK_OK=true
else
    echo "⚠️  VTK转换可能有问题"
    VTK_OK=false
fi

# 6. 最终报告
echo ""
echo "🎉 后处理完成!"
echo "📊 结果: $PROCESSED 个时间步有渗透率数据"
echo ""
echo "🚀 查看结果:"
if [ "$VTK_OK" = true ]; then
    echo "   paraview $(pwd)/VTK/*.vtm.series"
fi
echo "   paraview $(pwd)/permeability_post.foam"
echo "   cat $(pwd)/PERMEABILITY_SUMMARY.txt"
echo ""
echo "📋 可用字段: eps(孔隙度), K(渗透率), Kinv(渗透率倒数)"
