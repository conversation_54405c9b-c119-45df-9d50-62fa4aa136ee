# 🚀 reactiveTransportDBSFoam 渗透率后处理程序

## 📋 概述

这是专门为reactiveTransportDBSFoam算例设计的渗透率后处理程序。程序会：

1. **读取现有eps数据** - 从已完成的算例中读取所有时间步的孔隙度数据
2. **计算渗透率字段** - 基于Kozeny-Carman方程计算K和Kinv字段
3. **生成ParaView文件** - 创建可视化文件显示渗透率演化
4. **计算总渗透率** - 分析整个区域的渗透率变化

## 🔧 可用程序

### 1. 简化版 (推荐)
```bash
/home/<USER>/GeoChemFoam-5.1/simple_permeability_post.sh [算例目录]
```
**特点**: 简单快速，适合日常使用

### 2. Python版 (功能完整)
```bash
/home/<USER>/GeoChemFoam-5.1/permeability_postprocess.py [算例目录]
```
**特点**: 功能完整，详细分析

### 3. Shell版 (详细版)
```bash
/home/<USER>/GeoChemFoam-5.1/permeability_postprocess.sh [算例目录]
```
**特点**: 详细处理过程和报告

## 🎯 使用方法

### 前提条件
- ✅ reactiveTransportDBSFoam算例已运行完成
- ✅ 有完整的eps时间历程数据
- ✅ OpenFOAM环境已正确加载

### 基本使用
```bash
# 进入您的算例目录
cd your_case_directory

# 运行后处理 (推荐)
/home/<USER>/GeoChemFoam-5.1/simple_permeability_post.sh

# 或指定目录
/home/<USER>/GeoChemFoam-5.1/simple_permeability_post.sh /path/to/your/case
```

### 完整示例
```bash
# 示例: 处理3D方解石溶解算例
cd "/home/<USER>/GeoChemFoam-5.1/tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS"

# 运行后处理
/home/<USER>/GeoChemFoam-5.1/simple_permeability_post.sh

# 输出示例:
# ✅ 找到 8 个时间步: 800 → 6400
# ✅ 有eps数据: 8 个时间步
# ✅ 8 个时间步有渗透率数据
# 🚀 查看结果: paraview VTK/*.vtm.series
```

## 📊 程序功能

### 自动处理流程:
1. **✅ 检查算例** - 验证OpenFOAM算例目录
2. **✅ 重构数据** - 自动处理并行数据 (如果需要)
3. **✅ 查找时间步** - 识别所有有eps数据的时间步
4. **✅ 配置计算** - 创建Kozeny-Carman方程配置
5. **✅ 计算渗透率** - 为每个时间步计算K和Kinv字段
6. **✅ 分析总渗透率** - 统计各时间步的渗透率数据
7. **✅ 生成ParaView文件** - 创建.foam和VTK文件
8. **✅ 生成报告** - 创建详细的分析报告

### 渗透率计算公式:
```
K = eps³ / ((1-eps)² × 1.8×10¹²)
Kinv = 1.8×10¹² × (1-eps)² / eps³
```
基于Kozeny-Carman方程，其中kf = 1.8×10¹²

## 🎨 ParaView可视化

### 自动生成的文件:
- `permeability_post.foam` - OpenFOAM格式
- `VTK/*.vtm.series` - VTK格式 (推荐)

### 打开方式:
```bash
# VTK格式 (推荐)
paraview VTK/*.vtm.series

# OpenFOAM格式
paraview permeability_post.foam
```

### 可用字段:
- **eps**: 孔隙度 (所有时间步)
- **K**: 渗透率 (有eps数据的时间步)
- **Kinv**: 渗透率倒数
- **C**: 浓度 (原有字段)
- **U**: 速度 (原有字段)
- **p**: 压力 (原有字段)

### ParaView操作:
1. **加载数据**: 点击 Apply
2. **选择字段**: 勾选要显示的字段
3. **时间控制**: 使用时间滑块查看演化
4. **着色**: 选择字段进行着色显示
5. **可视化**: 创建等值面、切片等

## 📈 分析报告

### 生成的报告文件:
- `PERMEABILITY_SUMMARY.txt` - 简化版报告
- `PERMEABILITY_ANALYSIS.txt` - 详细版报告 (完整程序)

### 报告内容:
- 时间步信息
- 各时间步渗透率数据
- 渗透率演化趋势
- 总渗透率变化分析
- ParaView使用说明

### 查看报告:
```bash
cat PERMEABILITY_SUMMARY.txt
```

## 💡 使用技巧

### 1. 批量处理多个算例
```bash
for case in case1 case2 case3; do
    /home/<USER>/GeoChemFoam-5.1/simple_permeability_post.sh $case
done
```

### 2. 只处理特定时间步
```bash
# 编辑system/expressions后手动运行
postProcess -func expressions -time "6400"
```

### 3. 自定义渗透率公式
修改`system/expressions`文件中的expression部分

### 4. 并行处理
程序会自动检测并重构并行数据

## 🔍 故障排除

### 常见问题:

1. **"没有找到时间步数据"**
   - 确保算例已运行完成
   - 检查是否有数字命名的时间步文件夹

2. **"没有找到eps数据"**
   - 确保使用reactiveTransportDBSFoam求解器
   - 检查时间步文件夹中是否有eps字段文件

3. **"postProcess失败"**
   - 检查OpenFOAM环境是否正确加载
   - 确保有写入权限

4. **"VTK转换失败"**
   - 检查foamToVTK命令是否可用
   - 可以只使用.foam文件

## 🎉 输出结果

### 成功运行后您将得到:
- ✅ **完整的渗透率时间历程** - 每个时间步的K和Kinv字段
- ✅ **ParaView可视化文件** - 即开即用的可视化
- ✅ **详细分析报告** - 渗透率统计和演化分析
- ✅ **总渗透率数据** - 整个区域的渗透率变化

### 您可以分析:
- 孔隙度随时间的变化
- 渗透率的空间分布
- 渗透率随时间的演化
- 孔隙度-渗透率关系
- 总体渗透率变化趋势

---

**现在您可以轻松分析任何reactiveTransportDBSFoam算例的渗透率演化！** 🎯
