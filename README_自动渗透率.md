# 🚀 reactiveTransportDBSFoam 自动渗透率处理系统

## 📋 概述

这是一套完整的自动化脚本，用于处理reactiveTransportDBSFoam算例的渗透率计算和可视化。

## 🔧 可用脚本

### 1. 快速处理脚本 (推荐)
```bash
./quick_permeability.sh [算例目录]
```
**功能**: 一键完成所有处理，简单快速
**适用**: 日常使用，快速查看结果

### 2. 完整处理脚本
```bash
./auto_permeability.sh [算例目录]
```
**功能**: 详细处理过程，生成完整报告
**适用**: 需要详细信息和使用说明

### 3. ParaView启动脚本
```bash
./open_paraview.sh [算例目录]
```
**功能**: 自动启动ParaView并加载数据
**适用**: 处理完成后直接可视化

## 🎯 使用方法

### 方法1: 一键处理 (最简单)
```bash
# 进入算例目录
cd your_case_directory

# 一键处理
/home/<USER>/GeoChemFoam-5.1/quick_permeability.sh

# 自动打开ParaView
/home/<USER>/GeoChemFoam-5.1/open_paraview.sh
```

### 方法2: 指定目录处理
```bash
# 处理指定算例
/home/<USER>/GeoChemFoam-5.1/quick_permeability.sh /path/to/your/case

# 打开ParaView
/home/<USER>/GeoChemFoam-5.1/open_paraview.sh /path/to/your/case
```

### 方法3: 详细处理
```bash
# 完整处理 (包含详细报告)
/home/<USER>/GeoChemFoam-5.1/auto_permeability.sh your_case_directory
```

## 📊 脚本功能

### 自动处理内容:
1. ✅ **检查算例目录** - 验证OpenFOAM算例
2. ✅ **重构并行数据** - 自动处理processor文件夹
3. ✅ **配置渗透率计算** - 创建Kozeny-Carman方程配置
4. ✅ **批量计算渗透率** - 为所有时间步计算K和Kinv字段
5. ✅ **生成ParaView文件** - 创建.foam和VTK文件
6. ✅ **验证结果** - 检查生成的字段
7. ✅ **生成使用说明** - 创建详细的使用指南

### 渗透率计算公式:
```
K = eps³ / ((1-eps)² × 1.8e12)
Kinv = 1.8e12 × (1-eps)² / eps³
```
基于Kozeny-Carman方程，kf = 1.8×10¹²

## 🎨 ParaView使用

### 自动启动:
```bash
./open_paraview.sh
```

### 手动启动:
```bash
# VTK文件 (推荐)
paraview VTK/*.vtm.series

# OpenFOAM文件
paraview permeability_auto.foam
```

### 可用字段:
- **eps**: 孔隙度 (所有时间步)
- **K**: 渗透率 (有eps数据的时间步)
- **Kinv**: 渗透率倒数
- **C**: 浓度
- **U**: 速度
- **p**: 压力

### ParaView操作:
1. 点击 **Apply** 加载数据
2. 选择要显示的字段
3. 使用时间控制器查看不同时间步
4. 创建等值面、切片等可视化

## 📁 输出文件

处理完成后会生成:
- `permeability_auto.foam` - ParaView OpenFOAM文件
- `VTK/` - VTK格式文件夹
- `system/expressions` - 渗透率计算配置
- `PARAVIEW_USAGE.txt` - 详细使用说明 (完整脚本)

## 🔍 故障排除

### 常见问题:

1. **"不是有效的OpenFOAM算例"**
   - 确保目录包含system/和constant/文件夹

2. **"没有找到时间步数据"**
   - 确保算例已经运行完成
   - 检查是否有数字命名的时间步文件夹

3. **"没有找到eps数据"**
   - 确保使用的是reactiveTransportDBSFoam求解器
   - 检查时间步文件夹中是否有eps字段

4. **ParaView无法打开**
   - 检查ParaView是否正确安装
   - 尝试手动启动: `paraview`

## 💡 使用技巧

1. **批量处理多个算例**:
   ```bash
   for case in case1 case2 case3; do
       /home/<USER>/GeoChemFoam-5.1/quick_permeability.sh $case
   done
   ```

2. **只处理特定时间步**:
   编辑`system/expressions`文件，然后手动运行:
   ```bash
   postProcess -func expressions -time "6400"
   ```

3. **自定义渗透率公式**:
   修改`system/expressions`中的expression部分

## 🎉 示例用法

```bash
# 示例: 处理CaSO4沉淀算例
cd /home/<USER>/GeoChemFoam-5.1/tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS

# 一键处理
/home/<USER>/GeoChemFoam-5.1/quick_permeability.sh

# 输出:
# ✅ 处理完成!
# 📊 时间步: 8, 有渗透率: 1
# 🚀 打开ParaView: paraview VTK/*.vtm.series

# 自动打开ParaView
/home/<USER>/GeoChemFoam-5.1/open_paraview.sh
```

---

**现在您可以轻松处理任何reactiveTransportDBSFoam算例的渗透率可视化！** 🎯
