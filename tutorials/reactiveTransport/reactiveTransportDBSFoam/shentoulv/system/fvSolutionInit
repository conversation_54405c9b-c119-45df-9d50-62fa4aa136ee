/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2106                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      fvSolution;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

solvers
{
    pcorr
    {
        solver          PCG;
        preconditioner
        {
            preconditioner  GAMG;
            tolerance       0.001;
            relTol          0;
            smoother        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
            nPreSweeps      0;
            nPostSweeps     2;
            nBottomSweeps   2;
            cacheAgglomeration false;
            nCellsInCoarsestLevel 10;
            agglomerator    faceAreaPair;
            mergeLevels     1;
        }

        tolerance       0.0001;
        relTol          0;
        maxIter         100;
    }

    p
    {
        solver          PCG;
        preconditioner
        {
            preconditioner  GAMG;
            tolerance       0.001;
            relTol          0;
            smoother        GaussSeidel;
            nPreSweeps      0;
            nPostSweeps     2;
            nBottomSweeps   2;
            cacheAgglomeration false;
            nCellsInCoarsestLevel 10;
            agglomerator    faceAreaPair;
            mergeLevels     1;
        }

        tolerance       0.0001;
        relTol          0;
        maxIter         100;
    }


    U
    {
        solver          PBiCGStab;
        preconditioner  DILU;
        tolerance        1e-7;
        relTol           0.1;
    }

    Yi 
    {
        solver          PBiCGStab;
        preconditioner  DILU;
        tolerance       1e-9;
        relTol          0.1;
    }


    eps 
    {
        solver          BiCGStab;//PCG;
        preconditioner  DILU;//DIC;
        tolerance       1e-7;
        relTol          0;
    }
}


SIMPLE
{
    nNonOrthogonalCorrectors 0;

    nS nSmooth;
    cS cSmooth;

	residualControl
	{
		p               1e-5;
		U               1e-5;
	}
}

STEADYSTATE
{
    VoS iVoS;
    nNonOrthogonalCorrectors 0;
    nCorrectors 1;

	residualControl
	{
		p               1e-5;
		U               1e-5;
                C               1e-5;
	}
}

relaxationFactors
{
   U 0.3;
   p 0.3;
   C 0.3;
}

cache
{
    grad(U);
    grad(p);
}


// ************************************************************************* //
