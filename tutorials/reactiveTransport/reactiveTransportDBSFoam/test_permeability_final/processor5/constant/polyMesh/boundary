/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    outlet
    {
        type            patch;
        nFaces          240;
        startFace       16794;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17034;
    }
    walls
    {
        type            patch;
        nFaces          1201;
        startFace       17034;
    }
    procBoundary5to0
    {
        type            processor;
        inGroups        1(processor);
        nFaces          235;
        startFace       18235;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        5;
        neighbProcNo    0;
    }
    procBoundary5to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          35;
        startFace       18470;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        5;
        neighbProcNo    3;
    }
    procBoundary5to6
    {
        type            processor;
        inGroups        1(processor);
        nFaces          260;
        startFace       18505;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        5;
        neighbProcNo    6;
    }
    procBoundary5to9
    {
        type            processor;
        inGroups        1(processor);
        nFaces          507;
        startFace       18765;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        5;
        neighbProcNo    9;
    }
)

// ************************************************************************* //
