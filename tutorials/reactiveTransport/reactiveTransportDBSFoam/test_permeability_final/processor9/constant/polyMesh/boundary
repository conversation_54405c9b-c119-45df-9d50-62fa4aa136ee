/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    outlet
    {
        type            patch;
        nFaces          60;
        startFace       16952;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17012;
    }
    walls
    {
        type            patch;
        nFaces          1215;
        startFace       17012;
    }
    procBoundary9to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          146;
        startFace       18227;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        9;
        neighbProcNo    3;
    }
    procBoundary9to5
    {
        type            processor;
        inGroups        1(processor);
        nFaces          507;
        startFace       18373;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        9;
        neighbProcNo    5;
    }
    procBoundary9to7
    {
        type            processor;
        inGroups        1(processor);
        nFaces          178;
        startFace       18880;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        9;
        neighbProcNo    7;
    }
    procBoundary9to8
    {
        type            processor;
        inGroups        1(processor);
        nFaces          380;
        startFace       19058;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        9;
        neighbProcNo    8;
    }
)

// ************************************************************************* //
