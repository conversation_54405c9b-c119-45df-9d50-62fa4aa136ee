/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

5
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       16792;
    }
    inlet
    {
        type            patch;
        nFaces          180;
        startFace       16792;
    }
    walls
    {
        type            patch;
        nFaces          1546;
        startFace       16972;
    }
    procBoundary15to16
    {
        type            processor;
        inGroups        1(processor);
        nFaces          388;
        startFace       18518;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        15;
        neighbProcNo    16;
    }
    procBoundary15to17
    {
        type            processor;
        inGroups        1(processor);
        nFaces          182;
        startFace       18906;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        15;
        neighbProcNo    17;
    }
)

// ************************************************************************* //
