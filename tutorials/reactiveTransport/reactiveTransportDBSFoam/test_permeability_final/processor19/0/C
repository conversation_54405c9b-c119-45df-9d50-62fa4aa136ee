/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0";
    object      C;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 -3 0 0 1 0 0];

internalField   uniform 0;

boundaryField
{
    outlet
    {
        type            zeroGradient;
    }
    inlet
    {
        type            fixedValue;
        value           nonuniform List<scalar> 0();
    }
    walls
    {
        type            zeroGradient;
    }
    procBoundary19to3
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary19to4
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary19to10
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary19to11
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary19to12
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary19to18
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
