/*---------------------------------------------------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
Build  : b5c77f48-20220715 OPENFOAM=2206 version=v2206
Arch   : "LSB;label=32;scalar=64"
Exec   : decomposePar -constant
Date   : Jul 21 2025
Time   : 00:02:44
Host   : dyfluid-virtual-machine
PID    : 4385
I/O    : uncollated
Case   : /home/<USER>/GeoChemFoam-5.1/tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS
nProcs : 1
trapFpe: Floating point exception trapping enabled (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 5, maxFileModificationPolls 20)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time



Decomposing mesh

Create mesh

Calculating distribution of cells
Decomposition method scotch [20] (region region0)

Finished decomposition in 1.11 s

Calculating original mesh data

Distributing cells to processors

Distributing faces to processors

Distributing points to processors

Constructing processor meshes
Reading hexRef8 data : cellLevel
Reading hexRef8 data : pointLevel
Reading hexRef8 data : level0Edge
Reading hexRef8 data : refinementHistory

Processor 0
    Number of cells = 5954
    Number of points = 7205
    Number of faces shared with processor 1 = 338
    Number of faces shared with processor 3 = 106
    Number of faces shared with processor 5 = 235
    Number of faces shared with processor 6 = 281
    Number of processor patches = 4
    Number of processor faces = 960
    Number of boundary faces = 1400

Processor 1
    Number of cells = 5952
    Number of points = 7181
    Number of faces shared with processor 0 = 338
    Number of faces shared with processor 2 = 255
    Number of faces shared with processor 3 = 82
    Number of faces shared with processor 10 = 208
    Number of processor patches = 4
    Number of processor faces = 883
    Number of boundary faces = 1439

Processor 2
    Number of cells = 5952
    Number of points = 7494
    Number of faces shared with processor 1 = 255
    Number of faces shared with processor 3 = 392
    Number of faces shared with processor 10 = 78
    Number of faces shared with processor 11 = 219
    Number of processor patches = 4
    Number of processor faces = 944
    Number of boundary faces = 748

Processor 3
    Number of cells = 6072
    Number of points = 7677
    Number of faces shared with processor 0 = 106
    Number of faces shared with processor 1 = 82
    Number of faces shared with processor 2 = 392
    Number of faces shared with processor 4 = 425
    Number of faces shared with processor 5 = 35
    Number of faces shared with processor 7 = 41
    Number of faces shared with processor 9 = 146
    Number of faces shared with processor 11 = 45
    Number of faces shared with processor 19 = 90
    Number of processor patches = 9
    Number of processor faces = 1362
    Number of boundary faces = 918

Processor 4
    Number of cells = 6068
    Number of points = 7578
    Number of faces shared with processor 3 = 425
    Number of faces shared with processor 7 = 285
    Number of faces shared with processor 17 = 17
    Number of faces shared with processor 18 = 201
    Number of faces shared with processor 19 = 40
    Number of processor patches = 5
    Number of processor faces = 968
    Number of boundary faces = 806

Processor 5
    Number of cells = 6011
    Number of points = 7325
    Number of faces shared with processor 0 = 235
    Number of faces shared with processor 3 = 35
    Number of faces shared with processor 6 = 260
    Number of faces shared with processor 9 = 507
    Number of processor patches = 4
    Number of processor faces = 1037
    Number of boundary faces = 1441

Processor 6
    Number of cells = 5952
    Number of points = 7120
    Number of faces shared with processor 0 = 281
    Number of faces shared with processor 5 = 260
    Number of processor patches = 2
    Number of processor faces = 541
    Number of boundary faces = 1671

Processor 7
    Number of cells = 6070
    Number of points = 7312
    Number of faces shared with processor 3 = 41
    Number of faces shared with processor 4 = 285
    Number of faces shared with processor 8 = 160
    Number of faces shared with processor 9 = 178
    Number of faces shared with processor 17 = 190
    Number of processor patches = 5
    Number of processor faces = 854
    Number of boundary faces = 1494

Processor 8
    Number of cells = 6070
    Number of points = 7282
    Number of faces shared with processor 7 = 160
    Number of faces shared with processor 9 = 380
    Number of processor patches = 2
    Number of processor faces = 540
    Number of boundary faces = 1754

Processor 9
    Number of cells = 6065
    Number of points = 7383
    Number of faces shared with processor 3 = 146
    Number of faces shared with processor 5 = 507
    Number of faces shared with processor 7 = 178
    Number of faces shared with processor 8 = 380
    Number of processor patches = 4
    Number of processor faces = 1211
    Number of boundary faces = 1275

Processor 10
    Number of cells = 6040
    Number of points = 7303
    Number of faces shared with processor 1 = 208
    Number of faces shared with processor 2 = 78
    Number of faces shared with processor 11 = 303
    Number of faces shared with processor 12 = 43
    Number of faces shared with processor 14 = 290
    Number of faces shared with processor 19 = 12
    Number of processor patches = 6
    Number of processor faces = 934
    Number of boundary faces = 1448

Processor 11
    Number of cells = 5952
    Number of points = 7436
    Number of faces shared with processor 2 = 219
    Number of faces shared with processor 3 = 45
    Number of faces shared with processor 10 = 303
    Number of faces shared with processor 12 = 51
    Number of faces shared with processor 19 = 308
    Number of processor patches = 5
    Number of processor faces = 926
    Number of boundary faces = 740

Processor 12
    Number of cells = 6072
    Number of points = 7379
    Number of faces shared with processor 10 = 43
    Number of faces shared with processor 11 = 51
    Number of faces shared with processor 13 = 266
    Number of faces shared with processor 14 = 234
    Number of faces shared with processor 16 = 436
    Number of faces shared with processor 18 = 107
    Number of faces shared with processor 19 = 57
    Number of processor patches = 7
    Number of processor faces = 1194
    Number of boundary faces = 1274

Processor 13
    Number of cells = 5952
    Number of points = 7164
    Number of faces shared with processor 12 = 266
    Number of faces shared with processor 14 = 296
    Number of processor patches = 2
    Number of processor faces = 562
    Number of boundary faces = 1728

Processor 14
    Number of cells = 5958
    Number of points = 7132
    Number of faces shared with processor 10 = 290
    Number of faces shared with processor 12 = 234
    Number of faces shared with processor 13 = 296
    Number of processor patches = 3
    Number of processor faces = 820
    Number of boundary faces = 1404

Processor 15
    Number of cells = 5980
    Number of points = 7194
    Number of faces shared with processor 16 = 388
    Number of faces shared with processor 17 = 182
    Number of processor patches = 2
    Number of processor faces = 570
    Number of boundary faces = 1726

Processor 16
    Number of cells = 6072
    Number of points = 7345
    Number of faces shared with processor 12 = 436
    Number of faces shared with processor 15 = 388
    Number of faces shared with processor 17 = 34
    Number of faces shared with processor 18 = 143
    Number of processor patches = 4
    Number of processor faces = 1001
    Number of boundary faces = 1403

Processor 17
    Number of cells = 6072
    Number of points = 7292
    Number of faces shared with processor 4 = 17
    Number of faces shared with processor 7 = 190
    Number of faces shared with processor 15 = 182
    Number of faces shared with processor 16 = 34
    Number of faces shared with processor 18 = 315
    Number of processor patches = 5
    Number of processor faces = 738
    Number of boundary faces = 1570

Processor 18
    Number of cells = 6012
    Number of points = 7621
    Number of faces shared with processor 4 = 201
    Number of faces shared with processor 12 = 107
    Number of faces shared with processor 16 = 143
    Number of faces shared with processor 17 = 315
    Number of faces shared with processor 19 = 546
    Number of processor patches = 5
    Number of processor faces = 1312
    Number of boundary faces = 948

Processor 19
    Number of cells = 5964
    Number of points = 7474
    Number of faces shared with processor 3 = 90
    Number of faces shared with processor 4 = 40
    Number of faces shared with processor 10 = 12
    Number of faces shared with processor 11 = 308
    Number of faces shared with processor 12 = 57
    Number of faces shared with processor 18 = 546
    Number of processor patches = 6
    Number of processor faces = 1053
    Number of boundary faces = 785

Number of processor faces = 9205
Max number of cells = 6072 (0.998003992% above average 6012)
Max number of processor patches = 9 (104.5454545% above average 4.4)
Max number of faces between processors = 1362 (47.96306355% above average 920.5)

Time = constant

Processor 0: field transfer
Processor 1: field transfer
Processor 2: field transfer
Processor 3: field transfer
Processor 4: field transfer
Processor 5: field transfer
Processor 6: field transfer
Processor 7: field transfer
Processor 8: field transfer
Processor 9: field transfer
Processor 10: field transfer
Processor 11: field transfer
Processor 12: field transfer
Processor 13: field transfer
Processor 14: field transfer
Processor 15: field transfer
Processor 16: field transfer
Processor 17: field transfer
Processor 18: field transfer
Processor 19: field transfer
Time = 0

Processor 0: field transfer
Processor 1: field transfer
Processor 2: field transfer
Processor 3: field transfer
Processor 4: field transfer
Processor 5: field transfer
Processor 6: field transfer
Processor 7: field transfer
Processor 8: field transfer
Processor 9: field transfer
Processor 10: field transfer
Processor 11: field transfer
Processor 12: field transfer
Processor 13: field transfer
Processor 14: field transfer
Processor 15: field transfer
Processor 16: field transfer
Processor 17: field transfer
Processor 18: field transfer
Processor 19: field transfer

End

