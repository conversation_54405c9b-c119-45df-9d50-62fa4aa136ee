/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

9
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       16929;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       16929;
    }
    walls
    {
        type            patch;
        nFaces          1448;
        startFace       16929;
    }
    procBoundary10to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          208;
        startFace       18377;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        10;
        neighbProcNo    1;
    }
    procBoundary10to2
    {
        type            processor;
        inGroups        1(processor);
        nFaces          78;
        startFace       18585;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        10;
        neighbProcNo    2;
    }
    procBoundary10to11
    {
        type            processor;
        inGroups        1(processor);
        nFaces          303;
        startFace       18663;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        10;
        neighbProcNo    11;
    }
    procBoundary10to12
    {
        type            processor;
        inGroups        1(processor);
        nFaces          43;
        startFace       18966;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        10;
        neighbProcNo    12;
    }
    procBoundary10to14
    {
        type            processor;
        inGroups        1(processor);
        nFaces          290;
        startFace       19009;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        10;
        neighbProcNo    14;
    }
    procBoundary10to19
    {
        type            processor;
        inGroups        1(processor);
        nFaces          12;
        startFace       19299;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        10;
        neighbProcNo    19;
    }
)

// ************************************************************************* //
