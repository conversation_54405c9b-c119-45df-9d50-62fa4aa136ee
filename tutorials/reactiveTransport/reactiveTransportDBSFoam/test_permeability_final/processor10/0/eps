/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0";
    object      eps;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 0 0 0 0 0];

internalField   uniform 1;

boundaryField
{
    outlet
    {
        type            zeroGradient;
    }
    inlet
    {
        type            zeroGradient;
    }
    walls
    {
        type            zeroGradient;
    }
    procBoundary10to1
    {
        type            processor;
        value           uniform 1;
    }
    procBoundary10to2
    {
        type            processor;
        value           uniform 1;
    }
    procBoundary10to11
    {
        type            processor;
        value           uniform 1;
    }
    procBoundary10to12
    {
        type            processor;
        value           uniform 1;
    }
    procBoundary10to14
    {
        type            processor;
        value           uniform 1;
    }
    procBoundary10to19
    {
        type            processor;
        value           uniform 1;
    }
}


// ************************************************************************* //
