/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volVectorField;
    location    "0";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (0 0 0);

boundaryField
{
    outlet
    {
        type            zeroGradient;
    }
    inlet
    {
        type            flowRateInletVelocity;
        volumetricFlowRate constant 3.5e-10;
        extrapolateProfile true;
        value           nonuniform List<vector> 0();
    }
    walls
    {
        type            fixedValue;
        value           uniform (0 0 0);
    }
    procBoundary18to4
    {
        type            processor;
        value           uniform (0 0 0);
    }
    procBoundary18to12
    {
        type            processor;
        value           uniform (0 0 0);
    }
    procBoundary18to16
    {
        type            processor;
        value           uniform (0 0 0);
    }
    procBoundary18to17
    {
        type            processor;
        value           uniform (0 0 0);
    }
    procBoundary18to19
    {
        type            processor;
        value           uniform (0 0 0);
    }
}


// ************************************************************************* //
