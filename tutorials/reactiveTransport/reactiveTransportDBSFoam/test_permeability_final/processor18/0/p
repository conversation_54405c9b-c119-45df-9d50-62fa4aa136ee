/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0";
    object      p;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    outlet
    {
        type            fixedValue;
        value           nonuniform List<scalar> 0();
    }
    inlet
    {
        type            zeroGradient;
    }
    walls
    {
        type            zeroGradient;
    }
    procBoundary18to4
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary18to12
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary18to16
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary18to17
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary18to19
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
