/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

8
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       17293;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17293;
    }
    walls
    {
        type            patch;
        nFaces          948;
        startFace       17293;
    }
    procBoundary18to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          201;
        startFace       18241;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        18;
        neighbProcNo    4;
    }
    procBoundary18to12
    {
        type            processor;
        inGroups        1(processor);
        nFaces          107;
        startFace       18442;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        18;
        neighbProcNo    12;
    }
    procBoundary18to16
    {
        type            processor;
        inGroups        1(processor);
        nFaces          143;
        startFace       18549;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        18;
        neighbProcNo    16;
    }
    procBoundary18to17
    {
        type            processor;
        inGroups        1(processor);
        nFaces          315;
        startFace       18692;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        18;
        neighbProcNo    17;
    }
    procBoundary18to19
    {
        type            processor;
        inGroups        1(processor);
        nFaces          546;
        startFace       19007;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        18;
        neighbProcNo    19;
    }
)

// ************************************************************************* //
