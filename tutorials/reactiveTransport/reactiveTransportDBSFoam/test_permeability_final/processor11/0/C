/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0";
    object      C;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 -3 0 0 1 0 0];

internalField   uniform 0;

boundaryField
{
    outlet
    {
        type            zeroGradient;
    }
    inlet
    {
        type            fixedValue;
        value           nonuniform List<scalar> 0();
    }
    walls
    {
        type            zeroGradient;
    }
    procBoundary11to2
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary11to3
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary11to10
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary11to12
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary11to19
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
