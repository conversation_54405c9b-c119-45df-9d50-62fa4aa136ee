/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

5
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       16711;
    }
    inlet
    {
        type            patch;
        nFaces          320;
        startFace       16711;
    }
    walls
    {
        type            patch;
        nFaces          1408;
        startFace       17031;
    }
    procBoundary13to12
    {
        type            processor;
        inGroups        1(processor);
        nFaces          266;
        startFace       18439;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        13;
        neighbProcNo    12;
    }
    procBoundary13to14
    {
        type            processor;
        inGroups        1(processor);
        nFaces          296;
        startFace       18705;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        13;
        neighbProcNo    14;
    }
)

// ************************************************************************* //
