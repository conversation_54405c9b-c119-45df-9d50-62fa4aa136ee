/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       16682;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       16682;
    }
    walls
    {
        type            patch;
        nFaces          1400;
        startFace       16682;
    }
    procBoundary0to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          338;
        startFace       18082;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        0;
        neighbProcNo    1;
    }
    procBoundary0to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          106;
        startFace       18420;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        0;
        neighbProcNo    3;
    }
    procBoundary0to5
    {
        type            processor;
        inGroups        1(processor);
        nFaces          235;
        startFace       18526;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        0;
        neighbProcNo    5;
    }
    procBoundary0to6
    {
        type            processor;
        inGroups        1(processor);
        nFaces          281;
        startFace       18761;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        0;
        neighbProcNo    6;
    }
)

// ************************************************************************* //
