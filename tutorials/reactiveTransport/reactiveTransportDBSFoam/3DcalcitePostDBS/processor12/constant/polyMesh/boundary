/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

10
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       16982;
    }
    inlet
    {
        type            patch;
        nFaces          60;
        startFace       16982;
    }
    walls
    {
        type            patch;
        nFaces          1214;
        startFace       17042;
    }
    procBoundary12to10
    {
        type            processor;
        inGroups        1(processor);
        nFaces          43;
        startFace       18256;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        12;
        neighbProcNo    10;
    }
    procBoundary12to11
    {
        type            processor;
        inGroups        1(processor);
        nFaces          51;
        startFace       18299;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        12;
        neighbProcNo    11;
    }
    procBoundary12to13
    {
        type            processor;
        inGroups        1(processor);
        nFaces          266;
        startFace       18350;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        12;
        neighbProcNo    13;
    }
    procBoundary12to14
    {
        type            processor;
        inGroups        1(processor);
        nFaces          234;
        startFace       18616;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        12;
        neighbProcNo    14;
    }
    procBoundary12to16
    {
        type            processor;
        inGroups        1(processor);
        nFaces          436;
        startFace       18850;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        12;
        neighbProcNo    16;
    }
    procBoundary12to18
    {
        type            processor;
        inGroups        1(processor);
        nFaces          107;
        startFace       19286;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        12;
        neighbProcNo    18;
    }
    procBoundary12to19
    {
        type            processor;
        inGroups        1(processor);
        nFaces          57;
        startFace       19393;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        12;
        neighbProcNo    19;
    }
)

// ************************************************************************* //
