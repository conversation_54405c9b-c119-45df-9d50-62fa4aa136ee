/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

12
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       17451;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17451;
    }
    walls
    {
        type            patch;
        nFaces          918;
        startFace       17451;
    }
    procBoundary3to0
    {
        type            processor;
        inGroups        1(processor);
        nFaces          106;
        startFace       18369;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    0;
    }
    procBoundary3to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          82;
        startFace       18475;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    1;
    }
    procBoundary3to2
    {
        type            processor;
        inGroups        1(processor);
        nFaces          392;
        startFace       18557;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    2;
    }
    procBoundary3to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          425;
        startFace       18949;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    4;
    }
    procBoundary3to5
    {
        type            processor;
        inGroups        1(processor);
        nFaces          35;
        startFace       19374;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    5;
    }
    procBoundary3to7
    {
        type            processor;
        inGroups        1(processor);
        nFaces          41;
        startFace       19409;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    7;
    }
    procBoundary3to9
    {
        type            processor;
        inGroups        1(processor);
        nFaces          146;
        startFace       19450;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    9;
    }
    procBoundary3to11
    {
        type            processor;
        inGroups        1(processor);
        nFaces          45;
        startFace       19596;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    11;
    }
    procBoundary3to19
    {
        type            processor;
        inGroups        1(processor);
        nFaces          90;
        startFace       19641;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    19;
    }
)

// ************************************************************************* //
