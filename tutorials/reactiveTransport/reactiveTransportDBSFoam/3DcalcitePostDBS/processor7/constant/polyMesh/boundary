/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

8
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       17036;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17036;
    }
    walls
    {
        type            patch;
        nFaces          1494;
        startFace       17036;
    }
    procBoundary7to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          41;
        startFace       18530;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        7;
        neighbProcNo    3;
    }
    procBoundary7to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          285;
        startFace       18571;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        7;
        neighbProcNo    4;
    }
    procBoundary7to8
    {
        type            processor;
        inGroups        1(processor);
        nFaces          160;
        startFace       18856;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        7;
        neighbProcNo    8;
    }
    procBoundary7to9
    {
        type            processor;
        inGroups        1(processor);
        nFaces          178;
        startFace       19016;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        7;
        neighbProcNo    9;
    }
    procBoundary7to17
    {
        type            processor;
        inGroups        1(processor);
        nFaces          190;
        startFace       19194;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        7;
        neighbProcNo    17;
    }
)

// ************************************************************************* //
