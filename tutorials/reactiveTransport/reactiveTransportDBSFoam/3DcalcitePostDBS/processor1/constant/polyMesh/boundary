/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       16695;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       16695;
    }
    walls
    {
        type            patch;
        nFaces          1439;
        startFace       16695;
    }
    procBoundary1to0
    {
        type            processor;
        inGroups        1(processor);
        nFaces          338;
        startFace       18134;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        1;
        neighbProcNo    0;
    }
    procBoundary1to2
    {
        type            processor;
        inGroups        1(processor);
        nFaces          255;
        startFace       18472;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        1;
        neighbProcNo    2;
    }
    procBoundary1to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          82;
        startFace       18727;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        1;
        neighbProcNo    3;
    }
    procBoundary1to10
    {
        type            processor;
        inGroups        1(processor);
        nFaces          208;
        startFace       18809;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        1;
        neighbProcNo    10;
    }
)

// ************************************************************************* //
