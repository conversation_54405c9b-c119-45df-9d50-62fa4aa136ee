/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

5
(
    outlet
    {
        type            patch;
        nFaces          260;
        startFace       16750;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17010;
    }
    walls
    {
        type            patch;
        nFaces          1411;
        startFace       17010;
    }
    procBoundary6to0
    {
        type            processor;
        inGroups        1(processor);
        nFaces          281;
        startFace       18421;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    0;
    }
    procBoundary6to5
    {
        type            processor;
        inGroups        1(processor);
        nFaces          260;
        startFace       18702;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    5;
    }
)

// ************************************************************************* //
