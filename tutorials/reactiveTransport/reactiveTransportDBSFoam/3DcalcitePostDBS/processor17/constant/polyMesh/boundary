/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

8
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       17062;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17062;
    }
    walls
    {
        type            patch;
        nFaces          1570;
        startFace       17062;
    }
    procBoundary17to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          17;
        startFace       18632;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        17;
        neighbProcNo    4;
    }
    procBoundary17to7
    {
        type            processor;
        inGroups        1(processor);
        nFaces          190;
        startFace       18649;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        17;
        neighbProcNo    7;
    }
    procBoundary17to15
    {
        type            processor;
        inGroups        1(processor);
        nFaces          182;
        startFace       18839;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        17;
        neighbProcNo    15;
    }
    procBoundary17to16
    {
        type            processor;
        inGroups        1(processor);
        nFaces          34;
        startFace       19021;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        17;
        neighbProcNo    16;
    }
    procBoundary17to18
    {
        type            processor;
        inGroups        1(processor);
        nFaces          315;
        startFace       19055;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        17;
        neighbProcNo    18;
    }
)

// ************************************************************************* //
