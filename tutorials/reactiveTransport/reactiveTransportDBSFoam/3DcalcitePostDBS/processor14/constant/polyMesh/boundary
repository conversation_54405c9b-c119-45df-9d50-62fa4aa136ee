/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

6
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       16762;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       16762;
    }
    walls
    {
        type            patch;
        nFaces          1404;
        startFace       16762;
    }
    procBoundary14to10
    {
        type            processor;
        inGroups        1(processor);
        nFaces          290;
        startFace       18166;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        14;
        neighbProcNo    10;
    }
    procBoundary14to12
    {
        type            processor;
        inGroups        1(processor);
        nFaces          234;
        startFace       18456;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        14;
        neighbProcNo    12;
    }
    procBoundary14to13
    {
        type            processor;
        inGroups        1(processor);
        nFaces          296;
        startFace       18690;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        14;
        neighbProcNo    13;
    }
)

// ************************************************************************* //
