/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       17014;
    }
    inlet
    {
        type            patch;
        nFaces          190;
        startFace       17014;
    }
    walls
    {
        type            patch;
        nFaces          1213;
        startFace       17204;
    }
    procBoundary16to12
    {
        type            processor;
        inGroups        1(processor);
        nFaces          436;
        startFace       18417;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        16;
        neighbProcNo    12;
    }
    procBoundary16to15
    {
        type            processor;
        inGroups        1(processor);
        nFaces          388;
        startFace       18853;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        16;
        neighbProcNo    15;
    }
    procBoundary16to17
    {
        type            processor;
        inGroups        1(processor);
        nFaces          34;
        startFace       19241;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        16;
        neighbProcNo    17;
    }
    procBoundary16to18
    {
        type            processor;
        inGroups        1(processor);
        nFaces          143;
        startFace       19275;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        16;
        neighbProcNo    18;
    }
)

// ************************************************************************* //
