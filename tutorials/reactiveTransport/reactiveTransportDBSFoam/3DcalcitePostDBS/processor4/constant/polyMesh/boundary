/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

8
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       17860;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17860;
    }
    walls
    {
        type            patch;
        nFaces          806;
        startFace       17860;
    }
    procBoundary4to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          425;
        startFace       18666;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    3;
    }
    procBoundary4to7
    {
        type            processor;
        inGroups        1(processor);
        nFaces          285;
        startFace       19091;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    7;
    }
    procBoundary4to17
    {
        type            processor;
        inGroups        1(processor);
        nFaces          17;
        startFace       19376;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    17;
    }
    procBoundary4to18
    {
        type            processor;
        inGroups        1(processor);
        nFaces          201;
        startFace       19393;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    18;
    }
    procBoundary4to19
    {
        type            processor;
        inGroups        1(processor);
        nFaces          40;
        startFace       19594;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    19;
    }
)

// ************************************************************************* //
