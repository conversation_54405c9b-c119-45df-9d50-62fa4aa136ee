/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

5
(
    outlet
    {
        type            patch;
        nFaces          190;
        startFace       17063;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17253;
    }
    walls
    {
        type            patch;
        nFaces          1564;
        startFace       17253;
    }
    procBoundary8to7
    {
        type            processor;
        inGroups        1(processor);
        nFaces          160;
        startFace       18817;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        8;
        neighbProcNo    7;
    }
    procBoundary8to9
    {
        type            processor;
        inGroups        1(processor);
        nFaces          380;
        startFace       18977;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        8;
        neighbProcNo    9;
    }
)

// ************************************************************************* //
