/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

9
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       17483;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17483;
    }
    walls
    {
        type            patch;
        nFaces          785;
        startFace       17483;
    }
    procBoundary19to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          90;
        startFace       18268;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        19;
        neighbProcNo    3;
    }
    procBoundary19to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          40;
        startFace       18358;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        19;
        neighbProcNo    4;
    }
    procBoundary19to10
    {
        type            processor;
        inGroups        1(processor);
        nFaces          12;
        startFace       18398;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        19;
        neighbProcNo    10;
    }
    procBoundary19to11
    {
        type            processor;
        inGroups        1(processor);
        nFaces          308;
        startFace       18410;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        19;
        neighbProcNo    11;
    }
    procBoundary19to12
    {
        type            processor;
        inGroups        1(processor);
        nFaces          57;
        startFace       18718;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        19;
        neighbProcNo    12;
    }
    procBoundary19to18
    {
        type            processor;
        inGroups        1(processor);
        nFaces          546;
        startFace       18775;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        19;
        neighbProcNo    18;
    }
)

// ************************************************************************* //
