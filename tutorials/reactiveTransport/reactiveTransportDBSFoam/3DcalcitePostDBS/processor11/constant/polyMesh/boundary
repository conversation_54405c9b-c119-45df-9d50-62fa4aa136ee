/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

8
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       17593;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17593;
    }
    walls
    {
        type            patch;
        nFaces          740;
        startFace       17593;
    }
    procBoundary11to2
    {
        type            processor;
        inGroups        1(processor);
        nFaces          219;
        startFace       18333;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        11;
        neighbProcNo    2;
    }
    procBoundary11to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          45;
        startFace       18552;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        11;
        neighbProcNo    3;
    }
    procBoundary11to10
    {
        type            processor;
        inGroups        1(processor);
        nFaces          303;
        startFace       18597;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        11;
        neighbProcNo    10;
    }
    procBoundary11to12
    {
        type            processor;
        inGroups        1(processor);
        nFaces          51;
        startFace       18900;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        11;
        neighbProcNo    12;
    }
    procBoundary11to19
    {
        type            processor;
        inGroups        1(processor);
        nFaces          308;
        startFace       18951;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        11;
        neighbProcNo    19;
    }
)

// ************************************************************************* //
