/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       17625;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       17625;
    }
    walls
    {
        type            patch;
        nFaces          748;
        startFace       17625;
    }
    procBoundary2to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          255;
        startFace       18373;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        2;
        neighbProcNo    1;
    }
    procBoundary2to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          392;
        startFace       18628;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        2;
        neighbProcNo    3;
    }
    procBoundary2to10
    {
        type            processor;
        inGroups        1(processor);
        nFaces          78;
        startFace       19020;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        2;
        neighbProcNo    10;
    }
    procBoundary2to11
    {
        type            processor;
        inGroups        1(processor);
        nFaces          219;
        startFace       19098;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        2;
        neighbProcNo    11;
    }
)

// ************************************************************************* //
