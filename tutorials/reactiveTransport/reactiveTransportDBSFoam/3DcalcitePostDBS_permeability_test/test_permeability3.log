/*---------------------------------------------------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
Build  : b5c77f48-20220715 OPENFOAM=2206 version=v2206
Arch   : "LSB;label=32;scalar=64"
Exec   : reactiveTransportDBSFoam
Date   : Jul 30 2025
Time   : 22:48:51
Host   : dyfluid-virtual-machine
PID    : 32234
I/O    : uncollated
Case   : /home/<USER>/GeoChemFoam-5.1/tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS_permeability_test
nProcs : 1
trapFpe: Floating point exception trapping enabled (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 5, maxFileModificationPolls 20)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time

Create mesh for time = 0

Selecting dynamicFvMesh dynamicRefineFvMesh

SIMPLE: convergence criteria
    field p	 tolerance 1e-05
    field U	 tolerance 1e-05

Reading field p

Reading field U



--> FOAM FATAL IO ERROR: (openfoam-2206)
Unknown Function1 type flow_rate for volumetricFlowRate

Valid Function1 types :

25
(
coded
constant
cosine
csvFile
expression
functionObjectTrigger
functionObjectValue
halfCosineRamp
inputValueMapper
linearRamp
none
one
polynomial
quadraticRamp
quarterCosineRamp
quarterSineRamp
sample
scale
sine
square
step
table
tableFile
uniform
zero
)



file: 0/U.boundaryField.inlet at line 30 to 33.

    From static Foam::autoPtr<Foam::Function1<Type> > Foam::Function1<Type>::New(const Foam::word&, const Foam::entry*, const Foam::dictionary&, const Foam::word&, const Foam::objectRegistry*, bool) [with Type = double]
    in file /home/<USER>/OpenFOAM/OpenFOAM-v2206/src/OpenFOAM/lnInclude/Function1New.C at line 136.

FOAM exiting

