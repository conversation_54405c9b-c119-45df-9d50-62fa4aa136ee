/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2106                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volVectorField;
    location    "0";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (0 0 0); 

boundaryField
{
    outlet 
    {
        type            zeroGradient;
    }
    inlet 
    {
        type            flowRateInletVelocity; 
        extrapolateProfile true;
        volumetricFlowRate       flow_rate;
        value           uniform (0 0 0); 
    }
    walls
    {
        type            fixedValue;
        value           uniform (0 0 0);
    }
}


// ************************************************************************* //
