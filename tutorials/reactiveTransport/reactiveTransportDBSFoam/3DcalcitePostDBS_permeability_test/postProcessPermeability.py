#!/usr/bin/env python3
"""
Post-processing script for permeability analysis in reactiveTransportDBSFoam
"""

import os
import numpy as np
import matplotlib.pyplot as plt

def analyze_permeability(case_dir):
    """
    Analyze permeability data from OpenFOAM case
    """
    print("Permeability Analysis for reactiveTransportDBSFoam")
    print("=" * 50)
    
    # Find time directories
    time_dirs = []
    for item in os.listdir(case_dir):
        try:
            float(item)
            time_dirs.append(item)
        except ValueError:
            continue
    
    time_dirs.sort(key=float)
    
    if not time_dirs:
        print("No time directories found!")
        return
    
    print(f"Found {len(time_dirs)} time steps")
    print(f"Time range: {time_dirs[0]} to {time_dirs[-1]}")
    
    # Check for permeability fields
    latest_time = time_dirs[-1]
    latest_dir = os.path.join(case_dir, latest_time)
    
    fields = os.listdir(latest_dir)
    
    print(f"\nFields in latest time step ({latest_time}):")
    for field in sorted(fields):
        print(f"  - {field}")
    
    # Check for permeability fields
    has_K = 'K' in fields
    has_Kinv = 'Kinv' in fields
    has_eps = 'eps' in fields
    
    print(f"\nPermeability fields status:")
    print(f"  K (permeability): {'✓' if has_K else '✗'}")
    print(f"  Kinv (inverse permeability): {'✓' if has_Kinv else '✗'}")
    print(f"  eps (porosity): {'✓' if has_eps else '✗'}")
    
    if has_K:
        print(f"\n✓ SUCCESS: Permeability field K is available!")
        print("You can now visualize permeability in ParaView")
    else:
        print(f"\n✗ WARNING: Permeability field K not found!")
        print("Make sure you're using the modified reactiveTransportDBSFoam solver")
    
    return has_K, has_Kinv, has_eps

if __name__ == "__main__":
    case_dir = "."
    analyze_permeability(case_dir)
