/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "6400";
    object      cellLevel;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 0 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    outlet
    {
        type            calculated;
        value           nonuniform List<scalar> 0();
    }
    inlet
    {
        type            calculated;
        value           nonuniform List<scalar> 0();
    }
    walls
    {
        type            calculated;
        value           uniform 0;
    }
    procBoundary1to0
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary1to2
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary1to3
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary1to10
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
