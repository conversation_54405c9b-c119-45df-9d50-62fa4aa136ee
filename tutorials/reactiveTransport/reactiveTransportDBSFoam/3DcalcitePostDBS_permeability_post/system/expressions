expressions
{
    type            expressions;
    libs            ("libutilityFunctionObjects.so");
    expressions
    (
        {
            name        K;
            expression  "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/1.8e12";
            dimensions  [0 2 0 0 0 0 0];
        }
        {
            name        Kinv;
            expression  "1.8e12*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)";
            dimensions  [0 -2 0 0 0 0 0];
        }
    );
    writeControl    writeTime;
}
