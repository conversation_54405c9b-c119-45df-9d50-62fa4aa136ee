<?xml version='1.0'?>
<!-- time='6400' -->
<VTKFile type='vtkMultiBlockDataSet' version='1.0' byte_order='LittleEndian' header_type='UInt64'>
  <vtkMultiBlockDataSet>
    <DataSet name='internal' file='3DcalcitePostDBS（复件）（复件）_144/internal.vtu' />
    <Block name='boundary'>
      <DataSet name='outlet' file='3DcalcitePostDBS（复件）（复件）_144/boundary/outlet.vtp' />
      <DataSet name='inlet' file='3DcalcitePostDBS（复件）（复件）_144/boundary/inlet.vtp' />
      <DataSet name='walls' file='3DcalcitePostDBS（复件）（复件）_144/boundary/walls.vtp' />
    </Block>
  </vtkMultiBlockDataSet>
  <FieldData>
    <DataArray type='Float32' Name='TimeValue' NumberOfTuples='1' format='ascii'>
6400
    </DataArray>
  </FieldData>
</VTKFile>
