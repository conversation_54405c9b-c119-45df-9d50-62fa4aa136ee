/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0";
    object      Kinv;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 -2 0 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    outlet
    {
        type            calculated;
        value           nonuniform List<scalar> 0();
    }
    inlet
    {
        type            calculated;
        value           nonuniform List<scalar> 0();
    }
    walls
    {
        type            calculated;
        value           uniform 0;
    }
    procBoundary27to12
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary27to13
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary27to22
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary27to25
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary27to26
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary27to28
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary27to29
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
