/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

10
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       76910;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       76910;
    }
    walls
    {
        type            patch;
        nFaces          2660;
        startFace       76910;
    }
    procBoundary27to12
    {
        type            processor;
        inGroups        1(processor);
        nFaces          658;
        startFace       79570;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        27;
        neighbProcNo    12;
    }
    procBoundary27to13
    {
        type            processor;
        inGroups        1(processor);
        nFaces          262;
        startFace       80228;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        27;
        neighbProcNo    13;
    }
    procBoundary27to22
    {
        type            processor;
        inGroups        1(processor);
        nFaces          160;
        startFace       80490;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        27;
        neighbProcNo    22;
    }
    procBoundary27to25
    {
        type            processor;
        inGroups        1(processor);
        nFaces          580;
        startFace       80650;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        27;
        neighbProcNo    25;
    }
    procBoundary27to26
    {
        type            processor;
        inGroups        1(processor);
        nFaces          700;
        startFace       81230;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        27;
        neighbProcNo    26;
    }
    procBoundary27to28
    {
        type            processor;
        inGroups        1(processor);
        nFaces          580;
        startFace       81930;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        27;
        neighbProcNo    28;
    }
    procBoundary27to29
    {
        type            processor;
        inGroups        1(processor);
        nFaces          180;
        startFace       82510;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        27;
        neighbProcNo    29;
    }
)

// ************************************************************************* //
