/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2106                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      fvSolution;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

solvers
{

    p
    {
        solver          GAMG;
        tolerance       1e-6;
        relTol          0.1;
        smoother        G<PERSON>sSeidel;
        nPreSweeps      0;
        nPostSweeps     2;
        nFinestSweeps   2;
        cacheAgglomeration on;
        nCellsInCoarsestLevel 10;
        agglomerator    faceAreaPair;
        mergeLevels     1;
    }

    pcorr
    {
        $p
        tolerance       1e-8;
        relTol          0.001;
    }


    U
    {
        solver          PBiCGStab;
        preconditioner  DILU;
        tolerance        0;
        relTol           0.1;
    }

    Yi 
    {
        solver          PBiCGStab;
        preconditioner  DILU;
        tolerance       1e-6;
        relTol          0.1;
    }


    eps
    {
        solver          PCG;
        preconditioner  DIC;
        tolerance       1e-6;
        relTol          0;
    }
}


SIMPLE
{
    nNonOrthogonalCorrectors 1;

	residualControl
	{
		p               1e-5;
		U               1e-5;
	}
}

STEADYSTATE
{
    VoS iVoS;
    momentumPredictor no;
    nNonOrthogonalCorrectors 0;
    nCorrectors 5000;

	residualControl
	{
		p               1e-5;
		U               1e-5;
                C               1e-5;
	}
}

relaxationFactors
{
   U 0.3;
   C 0.3;
}

cache
{
    grad(U);
    grad(p);
}


// ************************************************************************* //
