/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  3.0.1                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      controlDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

application     reactiveTransportDDBSFoam;

startFrom       latestTime;

startTime       0;

stopAt          endTime;

endTime         6402;

deltaT          1;

writeControl    adjustableRunTime;

writeInterval   1;

purgeWrite      0;

writeFormat     ascii;

writePrecision  10;

writeCompression off;

timeFormat      general;

timePrecision   10;

runTimeModifiable yes;

adjustTimeStep yes;
maxDeltaEps 0.01;

maxDeltaT 50;

functions
{
    writePermeability
    {
        type            writeObjects;
        libs            ("libutilityFunctionObjects.so");
        objects         (K Kinv);
        writeControl    writeTime;
    }
}

// ************************************************************************* //
