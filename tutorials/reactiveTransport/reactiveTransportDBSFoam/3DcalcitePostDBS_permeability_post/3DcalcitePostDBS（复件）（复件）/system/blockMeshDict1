/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2106                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      blockMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

scale res;

lx0 xMin;
ly0 yMin;
lz0 zMin;

lx xMax;
ly yMax;
lz zMax;



vertices        
(
    ($lx0    $ly0    $lz0)
    ($lx     $ly0    $lz0)
    ($lx     $ly     $lz0)
    ($lx0    $ly     $lz0)
    ($lx0    $ly0    $lz)
    ($lx     $ly0    $lz)
    ($lx     $ly     $lz)
    ($lx0    $ly     $lz)
);

blocks          
(
    hex (0 1 2 3 4 5 6 7)  (nx ny nz) simpleGrading (1 1 1)  // (20 20 20) 
);

edges           
(
);

//patches  
boundary       
(

    outlet 
    {
        type patch;
        faces
        (
            (3 7 6 2)
        );
    }

    inlet 
    {
        type patch;
        faces
        (
            (1 5 4 0)
        );
    }

    walls 
    {
        type patch;
        faces
        (
            (0 4 7 3)
            (2 6 5 1)
            (0 3 2 1)
            (4 5 6 7)
        );
    }

);

mergePatchPairs 
(
);

// ************************************************************************* //
