/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

6
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       77520;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       77520;
    }
    walls
    {
        type            patch;
        nFaces          3400;
        startFace       77520;
    }
    procBoundary26to13
    {
        type            processor;
        inGroups        1(processor);
        nFaces          840;
        startFace       80920;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        26;
        neighbProcNo    13;
    }
    procBoundary26to27
    {
        type            processor;
        inGroups        1(processor);
        nFaces          700;
        startFace       81760;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        26;
        neighbProcNo    27;
    }
    procBoundary26to29
    {
        type            processor;
        inGroups        1(processor);
        nFaces          820;
        startFace       82460;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        26;
        neighbProcNo    29;
    }
)

// ************************************************************************* //
