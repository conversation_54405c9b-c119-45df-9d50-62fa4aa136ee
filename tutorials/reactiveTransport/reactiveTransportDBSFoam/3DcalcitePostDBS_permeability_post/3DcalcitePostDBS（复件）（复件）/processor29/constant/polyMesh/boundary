/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       76968;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       76968;
    }
    walls
    {
        type            patch;
        nFaces          3224;
        startFace       76968;
    }
    procBoundary29to17
    {
        type            processor;
        inGroups        1(processor);
        nFaces          800;
        startFace       80192;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        29;
        neighbProcNo    17;
    }
    procBoundary29to26
    {
        type            processor;
        inGroups        1(processor);
        nFaces          820;
        startFace       80992;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        29;
        neighbProcNo    26;
    }
    procBoundary29to27
    {
        type            processor;
        inGroups        1(processor);
        nFaces          180;
        startFace       81812;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        29;
        neighbProcNo    27;
    }
    procBoundary29to28
    {
        type            processor;
        inGroups        1(processor);
        nFaces          880;
        startFace       81992;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        29;
        neighbProcNo    28;
    }
)

// ************************************************************************* //
