/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0";
    object      p;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    outlet
    {
        type            fixedValue;
        value           nonuniform List<scalar> 0();
    }
    inlet
    {
        type            zeroGradient;
    }
    walls
    {
        type            zeroGradient;
    }
    procBoundary28to16
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary28to17
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary28to21
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary28to22
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary28to27
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary28to29
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
