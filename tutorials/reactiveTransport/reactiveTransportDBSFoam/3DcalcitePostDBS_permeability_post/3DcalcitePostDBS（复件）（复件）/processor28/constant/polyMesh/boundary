/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

9
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       77991;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       77991;
    }
    walls
    {
        type            patch;
        nFaces          2698;
        startFace       77991;
    }
    procBoundary28to16
    {
        type            processor;
        inGroups        1(processor);
        nFaces          620;
        startFace       80689;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        28;
        neighbProcNo    16;
    }
    procBoundary28to17
    {
        type            processor;
        inGroups        1(processor);
        nFaces          160;
        startFace       81309;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        28;
        neighbProcNo    17;
    }
    procBoundary28to21
    {
        type            processor;
        inGroups        1(processor);
        nFaces          60;
        startFace       81469;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        28;
        neighbProcNo    21;
    }
    procBoundary28to22
    {
        type            processor;
        inGroups        1(processor);
        nFaces          900;
        startFace       81529;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        28;
        neighbProcNo    22;
    }
    procBoundary28to27
    {
        type            processor;
        inGroups        1(processor);
        nFaces          580;
        startFace       82429;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        28;
        neighbProcNo    27;
    }
    procBoundary28to29
    {
        type            processor;
        inGroups        1(processor);
        nFaces          880;
        startFace       83009;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        28;
        neighbProcNo    29;
    }
)

// ************************************************************************* //
