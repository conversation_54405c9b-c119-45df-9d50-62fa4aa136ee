<?xml version='1.0'?>
<!-- time='4000' -->
<VTKFile type='vtkMultiBlockDataSet' version='1.0' byte_order='LittleEndian' header_type='UInt64'>
  <vtkMultiBlockDataSet>
    <DataSet name='internal' file='3DcalcitePostDBS_permeability_post_96/internal.vtu' />
    <Block name='boundary'>
      <DataSet name='outlet' file='3DcalcitePostDBS_permeability_post_96/boundary/outlet.vtp' />
      <DataSet name='inlet' file='3DcalcitePostDBS_permeability_post_96/boundary/inlet.vtp' />
      <DataSet name='walls' file='3DcalcitePostDBS_permeability_post_96/boundary/walls.vtp' />
    </Block>
  </vtkMultiBlockDataSet>
  <FieldData>
    <DataArray type='Float32' Name='TimeValue' NumberOfTuples='1' format='ascii'>
4000
    </DataArray>
  </FieldData>
</VTKFile>
