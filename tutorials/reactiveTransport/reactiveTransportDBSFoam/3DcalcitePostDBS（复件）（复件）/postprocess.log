/*---------------------------------------------------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
Build  : b5c77f48-20220715 OPENFOAM=2206 version=v2206
Arch   : "LSB;label=32;scalar=64"
Exec   : postProcess -func expressions -time (6400)
Date   : Jul 31 2025
Time   : 11:14:27
Host   : dyfluid-virtual-machine
PID    : 51740
I/O    : uncollated
Case   : /home/<USER>/GeoChemFoam-5.1/tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS（复件）（复件）
nProcs : 1
trapFpe: Floating point exception trapping enabled (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 5, maxFileModificationPolls 20)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time

Bad scalar-range parsing: "(6400)"
--> FOAM Warning : 
    From static Foam::instantList Foam::timeSelector::select0(Foam::Time&, const Foam::argList&)
    in file db/Time/timeSelector.C at line 252
    No time specified or available, selecting 'constant'
Create mesh for time = constant

--> FOAM Warning : 
Unknown function type expressions

Valid function types :

27
(
BilgerMixtureFraction
abort
areaWrite
coded
ensightWrite
multiRegion
parProfiling
patchProbes
probes
psiReactionThermoMoleFractions
psiSpecieReactionRates
removeRegisteredObject
rhoReactionThermoMoleFractions
rhoSpecieReactionRates
runTimeControl
setTimeStep
sets
solverInfo
surfaces
syncObjects
systemCall
thermoCoupleProbes
timeActivatedFileUpdate
timeInfo
vtkWrite
writeDictionary
writeObjects
)



    From static Foam::autoPtr<Foam::functionObject> Foam::functionObject::New(const Foam::word&, const Foam::Time&, const Foam::dictionary&)
    in file db/functionObjects/functionObject/functionObject.C at line 129.
--> loading function object 'expressions'

Time = constant

Reading fields:

Executing functionObjects

End

