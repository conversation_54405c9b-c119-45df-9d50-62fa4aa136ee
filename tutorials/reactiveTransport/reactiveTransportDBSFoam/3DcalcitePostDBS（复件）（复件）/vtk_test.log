/*---------------------------------------------------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
Build  : b5c77f48-20220715 OPENFOAM=2206 version=v2206
Arch   : "LSB;label=32;scalar=64"
Exec   : foamToVTK -latestTime
Date   : Jul 30 2025
Time   : 23:20:30
Host   : dyfluid-virtual-machine
PID    : 37081
I/O    : uncollated
Case   : /home/<USER>/GeoChemFoam-5.1/tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS（复件）（复件）
nProcs : 1
trapFpe: Floating point exception trapping enabled (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 5, maxFileModificationPolls 20)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time

Initial memory 189936 kB
Create mesh for time = 6400

VTK mesh topology: 5.41 s, 344184 kB
Time: 6400
    volScalar    : C K Kinv R cellLevel ddt0(eps) eps p
    volVector    : U
    Internal  : "VTK/3DcalcitePostDBS（复件）（复件）_144/internal.vtu"
    Boundary  : "VTK/3DcalcitePostDBS（复件）（复件）_144/boundary/outlet.vtp"
    Boundary  : "VTK/3DcalcitePostDBS（复件）（复件）_144/boundary/inlet.vtp"
    Boundary  : "VTK/3DcalcitePostDBS（复件）（复件）_144/boundary/walls.vtp"
Wrote in 17.82 s, 837952 kB

End: 23.23 s, 857948 kB (peak)

