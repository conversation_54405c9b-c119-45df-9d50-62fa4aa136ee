/*---------------------------------------------------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
Build  : b5c77f48-20220715 OPENFOAM=2206 version=v2206
Arch   : "LSB;label=32;scalar=64"
Exec   : reactiveTransportDBSCaSO4Foam
Date   : Jul 17 2025
Time   : 21:50:26
Host   : dyfluid-virtual-machine
PID    : 100545
I/O    : uncollated
Case   : /home/<USER>/GeoChemFoam-5.1/tutorials/reactiveTransport/reactiveTransportDBSCaSO4Foam/CaSO4PrecipitationSimple
nProcs : 1
trapFpe: Floating point exception trapping enabled (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 5, maxFileModificationPolls 20)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time

Create mesh for time = 0


SIMPLE: convergence criteria
    field p	 tolerance 0.0001
    field U	 tolerance 0.0001
    field "Yi.*"	 tolerance 1e-06

Reading field p

Reading field U

Reading transportProperties

Reading/calculating face flux field phi

Create species mixture

Read species diffusion coefficients

Selecting incompressible transport model Newtonian
Selecting turbulence model type laminar
Selecting laminar stress model Stokes
Reading reaction rate if present

No MRF models present

No finite volume options present
Max Delta eps: 0

Starting time loop

Courant Number mean: 5e-05 max: 0.0025
Max Delta eps: 0
deltaT = 0.1204819277
Time = 0.1204819277

R=sum((R*V)) [0 3 -1 0 0 0 0] 0
diagonal:  Solving for eps, Initial residual = 0, Final residual = 0, No Iterations 0
fluid fraction  Min(eps) = 0.5  Max(eps) = 0.5
psiCoeff=1

STEADYSTATE: no convergence criteria found. Calculations will run for 100 steps.

DILUPBiCGStab:  Solving for Ux, Initial residual = 0.9999999004, Final residual = 8.274373328e-11, No Iterations 1
DILUPBiCGStab:  Solving for Uy, Initial residual = 0, Final residual = 0, No Iterations 0
DILUPBiCGStab:  Solving for Uz, Initial residual = 0, Final residual = 0, No Iterations 0
GAMG:  Solving for p, Initial residual = 0.999999999, Final residual = 0.05652030954, No Iterations 3
time step continuity errors : sum local = 6.809675855e-06, global = 1.957703264e-07, cumulative = 1.957703264e-07


--> FOAM FATAL ERROR: (openfoam-2206)
Different dimensions for '(a = b)'
     dimensions : [0 0 -1 0 0 0 0] != [0 -1 -1 0 0 0 0]


    From bool Foam::checkDims(const char*, const Foam::dimensionSet&, const Foam::dimensionSet&)
    in file dimensionSet/dimensionSet.C at line 56.

FOAM aborting

#0  Foam::error::printStack(Foam::Ostream&) at ??:?
#1  Foam::error::simpleExit(int, bool) at ??:?
#2  Foam::dimensionSet::operator=(Foam::dimensionSet const&) const at ??:?
#3  Foam::GeometricField<double, Foam::fvPatchField, Foam::volMesh>::operator=(Foam::tmp<Foam::GeometricField<double, Foam::fvPatchField, Foam::volMesh> > const&) in ~/GeoChemFoam-5.1/bin/reactiveTransportDBSCaSO4Foam
#4  ? in ~/GeoChemFoam-5.1/bin/reactiveTransportDBSCaSO4Foam
#5  ? in /lib/x86_64-linux-gnu/libc.so.6
#6  __libc_start_main in /lib/x86_64-linux-gnu/libc.so.6
#7  ? in ~/GeoChemFoam-5.1/bin/reactiveTransportDBSCaSO4Foam
