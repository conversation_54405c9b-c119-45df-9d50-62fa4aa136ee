#!/bin/bash

###### Simple CaSO4 Precipitation Case Runner ######################

echo "=========================================="
echo "Simple CaSO4 Precipitation Case"
echo "reactiveTransportDBSCaSO4Foam solver"
echo "=========================================="

echo "Step 1: Creating mesh..."
blockMesh > blockMesh.out 2>&1
if [ $? -ne 0 ]; then
    echo "Error: blockMesh failed! Check blockMesh.out"
    exit 1
fi
echo "Mesh created successfully"

echo "Step 2: Initializing case..."
chmod +x initCase.sh
./initCase.sh
echo "Case initialized"

echo "Step 3: Running simple CaSO4 precipitation simulation..."
reactiveTransportDBSCaSO4Foam > reactiveTransportDBSCaSO4Foam.out 2>&1 &
SOLVER_PID=$!

echo "Solver started with PID: $SOLVER_PID"
echo "Monitor progress with: tail -f reactiveTransportDBSCaSO4Foam.out"
echo "Stop simulation with: kill $SOLVER_PID"

# Wait for solver to finish or user interruption
wait $SOLVER_PID
SOLVER_EXIT_CODE=$?

if [ $SOLVER_EXIT_CODE -eq 0 ]; then
    echo "=========================================="
    echo "Simple CaSO4 Precipitation simulation completed successfully!"
    echo "=========================================="
else
    echo "=========================================="
    echo "Simulation terminated with exit code: $SOLVER_EXIT_CODE"
    echo "Check reactiveTransportDBSCaSO4Foam.out for details"
    echo "=========================================="
fi

echo ""
echo "Results can be visualized in ParaView:"
echo "paraFoam"
echo ""
echo "Key output files:"
echo "- Concentration fields: Ca, SO4, Cl"
echo "- Porosity field: eps"
echo "- Velocity field: U"
echo "- Pressure field: p"
