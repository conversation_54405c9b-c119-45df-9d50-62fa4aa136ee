/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2106                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "constant";
    object      transportProperties;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

transportModel  Newtonian;

// Kinematic viscosity [m2/s]
nu              nu [ 0 2 -1 0 0 0 0 ] 1e-6;

// Molecular weight of precipitating mineral (CaSO4·2H2O) [kg/mol]
Mw              Mw [ 1 0 0 0 -1 0 0 ] 0.172;  // 172 g/mol for gypsum

// Density of solid phase (gypsum) [kg/m3]
rhos            rhos [ 1 -3 0 0 0 0 0 ] 2320;

// Kozeny-Carman constant [1/m2]
kf              kf [0 -2 0 0 0 0 0] 1.8e12;

// Volume of Solid scheme
VoS             "VoS-psi";

// Psi coefficient for VoS-psi scheme
psiCoeff        1.0;

// Adaptive psi coefficient
adaptPsiCoeff   false;

// ************************************************************************* //
