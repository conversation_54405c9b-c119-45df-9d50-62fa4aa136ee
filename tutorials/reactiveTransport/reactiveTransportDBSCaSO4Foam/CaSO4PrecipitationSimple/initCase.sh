#!/bin/bash

###### USERS INPUT ############################################################

# Define flow rate [m/s]
flowrate=1e-5

# Inlet concentrations for CaSO4 precipitation (mol/m³)
Ca_inlet=0.01      # High Ca concentration for supersaturation
SO4_inlet=0.01     # High SO4 concentration for supersaturation  
Cl_inlet=0.02      # Cl for charge balance

#### END OF USER INPUT #######################################################

echo "Initializing simple CaSO4 precipitation case"

# Copy original files to working directory
rm -rf 0
cp -r 0_orig 0

# Set flow rate
sed -i "s/flowrate/$flowrate/g" 0/U

# Set inlet concentrations
sed -i "s/Ca_inlet/$Ca_inlet/g" 0/Ca
sed -i "s/SO4_inlet/$SO4_inlet/g" 0/SO4
sed -i "s/Cl_inlet/$Cl_inlet/g" 0/Cl

echo "Flow rate set to: $flowrate m/s"
echo "Inlet concentrations set:"
echo "  Ca: $Ca_inlet mol/m³"
echo "  SO4: $SO4_inlet mol/m³"
echo "  Cl: $Cl_inlet mol/m³"

echo "Case initialized successfully!"
echo "Run 'blockMesh' to create the mesh"
echo "Then run 'reactiveTransportDBSCaSO4Foam' to start the simulation"
