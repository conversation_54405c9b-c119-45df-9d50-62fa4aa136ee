#!/bin/bash

# 后处理计算渗透率 - 基于现有eps数据
# 用法: ./calculate_permeability_post.sh [算例目录]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔬 后处理计算渗透率 (基于现有eps数据)${NC}"
echo "======================================================="

# 设置算例目录
if [ -z "$1" ]; then
    CASE_DIR="."
else
    CASE_DIR="$1"
fi

echo -e "${YELLOW}📁 算例目录: $CASE_DIR${NC}"

# 检查算例目录
if [ ! -d "$CASE_DIR" ]; then
    echo -e "${RED}❌ 算例目录不存在: $CASE_DIR${NC}"
    exit 1
fi

cd "$CASE_DIR"

# 第一步：重构并行数据（如果需要）
echo -e "${YELLOW}🔄 步骤1: 检查并重构并行数据...${NC}"

if [ -d "processor0" ] && [ ! -d "0" ]; then
    echo -e "${BLUE}📦 发现并行数据，开始重构...${NC}"
    reconstructPar > reconstruct.log 2>&1
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 并行数据重构成功${NC}"
    else
        echo -e "${YELLOW}⚠️  并行数据重构失败，尝试继续...${NC}"
    fi
fi

# 第二步：找到所有时间步
echo -e "${YELLOW}🔍 步骤2: 查找所有时间步...${NC}"

# 只匹配纯数字目录，排除0_orig等
TIME_DIRS=($(find . -maxdepth 1 -name "[0-9]*" -type d | sed 's|./||' | grep -E '^[0-9]+(\.[0-9]+)?$' | sort -n))

if [ ${#TIME_DIRS[@]} -eq 0 ]; then
    echo -e "${RED}❌ 没有找到时间步数据${NC}"
    echo -e "${YELLOW}💡 请确保算例已经运行完成${NC}"
    exit 1
fi

echo -e "${GREEN}📅 找到 ${#TIME_DIRS[@]} 个时间步: ${TIME_DIRS[0]} 到 ${TIME_DIRS[-1]}${NC}"

# 第三步：检查eps数据
echo -e "${YELLOW}🔍 步骤3: 检查eps数据...${NC}"

EPS_COUNT=0
for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ]; then
        ((EPS_COUNT++))
    fi
done

echo -e "${GREEN}📊 有eps数据的时间步: $EPS_COUNT / ${#TIME_DIRS[@]}${NC}"

if [ $EPS_COUNT -eq 0 ]; then
    echo -e "${RED}❌ 没有找到eps数据${NC}"
    exit 1
fi

# 第四步：创建postProcess配置
echo -e "${YELLOW}🔧 步骤4: 创建渗透率计算配置...${NC}"

mkdir -p system

# Kozeny-Carman参数
KF=1.8e12  # Kozeny-Carman常数

cat > system/permeabilityCalc << EOF
permeabilityCalc
{
    type            expressions;
    libs            ("libutilityFunctionObjects.so");
    
    expressions
    (
        {
            name        K;
            expression  "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/$KF";
            dimensions  [0 2 0 0 0 0 0];
        }
        {
            name        Kinv;
            expression  "$KF*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)";
            dimensions  [0 -2 0 0 0 0 0];
        }
    );
    
    writeControl    writeTime;
}
EOF

echo -e "${GREEN}✅ 配置文件创建完成 (kf = $KF)${NC}"

# 第五步：批量计算渗透率
echo -e "${YELLOW}🔧 步骤5: 批量计算渗透率...${NC}"

PROCESSED=0
FAILED=0

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ]; then
        echo -ne "${BLUE}📊 处理时间步 $time...${NC}\r"
        
        # 检查是否已经有渗透率字段
        if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
            echo -e "${GREEN}✅ 时间步 $time 已有渗透率字段                    ${NC}"
            ((PROCESSED++))
            continue
        fi
        
        # 使用postProcess计算
        if postProcess -func permeabilityCalc -time "$time" > /dev/null 2>&1; then
            if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
                ((PROCESSED++))
                echo -e "${GREEN}✅ 时间步 $time 渗透率计算成功                    ${NC}"
            else
                echo -e "${YELLOW}⚠️  时间步 $time postProcess未生成字段                    ${NC}"
                ((FAILED++))
            fi
        else
            echo -e "${RED}❌ 时间步 $time postProcess失败                    ${NC}"
            ((FAILED++))
        fi
    fi
done

echo ""
echo -e "${GREEN}📊 渗透率计算完成: $PROCESSED 成功, $FAILED 失败${NC}"

# 第六步：生成ParaView文件
echo -e "${YELLOW}🎨 步骤6: 生成ParaView文件...${NC}"

# 清理旧文件
rm -f *.foam *.OpenFOAM
rm -rf VTK/

# 创建新的.foam文件
touch permeability_complete.foam
echo -e "${GREEN}✅ 创建 permeability_complete.foam${NC}"

# 转换为VTK格式
echo -e "${BLUE}🔄 转换为VTK格式...${NC}"
foamToVTK > vtk_convert.log 2>&1

if [ -d "VTK" ]; then
    echo -e "${GREEN}✅ VTK文件生成成功${NC}"
else
    echo -e "${YELLOW}⚠️  VTK转换可能有问题，但.foam文件可用${NC}"
fi

# 第七步：验证结果
echo -e "${YELLOW}🔍 步骤7: 验证结果...${NC}"

TOTAL_WITH_K=0
TOTAL_WITH_KINV=0
SAMPLE_TIME=""

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ]; then
        [ -f "$time/K" ] && ((TOTAL_WITH_K++))
        [ -f "$time/Kinv" ] && ((TOTAL_WITH_KINV++))
        [ -z "$SAMPLE_TIME" ] && [ -f "$time/K" ] && SAMPLE_TIME="$time"
    fi
done

echo -e "${BLUE}📊 最终统计:${NC}"
echo -e "   有eps数据的时间步: $EPS_COUNT"
echo -e "   有K字段的时间步: $TOTAL_WITH_K"
echo -e "   有Kinv字段的时间步: $TOTAL_WITH_KINV"

# 显示样本数据
if [ -n "$SAMPLE_TIME" ] && [ -f "$SAMPLE_TIME/K" ]; then
    echo -e "${BLUE}📈 样本数据 (时间步 $SAMPLE_TIME):${NC}"
    
    # 获取K字段的数值范围
    if grep -q "uniform" "$SAMPLE_TIME/K"; then
        K_VALUE=$(grep "internalField.*uniform" "$SAMPLE_TIME/K" | awk '{print $3}' | sed 's/;//')
        echo -e "   K (uniform): $K_VALUE m²"
    else
        echo -e "   K: 非uniform字段"
    fi
    
    if grep -q "uniform" "$SAMPLE_TIME/Kinv"; then
        KINV_VALUE=$(grep "internalField.*uniform" "$SAMPLE_TIME/Kinv" | awk '{print $3}' | sed 's/;//')
        echo -e "   Kinv (uniform): $KINV_VALUE 1/m²"
    else
        echo -e "   Kinv: 非uniform字段"
    fi
fi

# 最终报告
echo ""
if [ "$TOTAL_WITH_K" -eq "$EPS_COUNT" ] && [ "$TOTAL_WITH_KINV" -eq "$EPS_COUNT" ]; then
    echo -e "${GREEN}🎉 SUCCESS! 所有时间步的渗透率计算完成!${NC}"
    
    echo ""
    echo -e "${BLUE}🚀 在ParaView中查看:${NC}"
    if [ -d "VTK" ]; then
        echo -e "${GREEN}   推荐: paraview VTK/*.vtm.series${NC}"
    fi
    echo -e "   备选: paraview permeability_complete.foam"
    
    echo ""
    echo -e "${BLUE}📊 可用字段 (所有时间步 0-${TIME_DIRS[-1]}):${NC}"
    echo -e "   ✅ K (渗透率) - 基于Kozeny-Carman方程"
    echo -e "   ✅ Kinv (渗透率倒数)"
    echo -e "   ✅ eps (孔隙度) - 原始数据"
    echo -e "   ✅ 所有其他原有字段"
    
    echo ""
    echo -e "${GREEN}🎯 按您的思路完美实现! 现在可以分析整个时间历程的渗透率变化了!${NC}"
    
else
    echo -e "${YELLOW}⚠️  部分时间步的渗透率计算失败${NC}"
    echo -e "${YELLOW}💡 但已有的数据仍可在ParaView中查看${NC}"
fi
