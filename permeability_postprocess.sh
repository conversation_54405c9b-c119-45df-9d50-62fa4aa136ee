#!/bin/bash

# reactiveTransportDBSFoam 渗透率后处理脚本
# 基于现有eps数据计算渗透率并分析总渗透率
# 用法: ./permeability_postprocess.sh [算例目录]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║           reactiveTransportDBSFoam 渗透率后处理              ║"
echo "║              基于现有eps数据计算渗透率                       ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 设置算例目录
CASE_DIR="${1:-.}"
echo -e "${BLUE}📁 算例目录: $CASE_DIR${NC}"

# 检查目录
if [ ! -d "$CASE_DIR/system" ] || [ ! -d "$CASE_DIR/constant" ]; then
    echo -e "${RED}❌ 不是有效的OpenFOAM算例目录${NC}"
    exit 1
fi

cd "$CASE_DIR"

# Kozeny-Carman参数
KF=1.8e12

echo -e "${YELLOW}🔄 步骤1: 重构并行数据...${NC}"
if [ -d "processor0" ]; then
    echo -e "${CYAN}   重构并行数据...${NC}"
    reconstructPar > reconstruct.log 2>&1 || echo -e "${YELLOW}   ⚠️  重构失败，继续处理...${NC}"
else
    echo -e "${GREEN}   ✅ 使用串行数据${NC}"
fi

echo -e "${YELLOW}🔍 步骤2: 查找时间步数据...${NC}"
TIME_DIRS=($(find . -maxdepth 1 -name "[0-9]*" -type d | sed 's|./||' | grep -E '^[0-9]+(\.[0-9]+)?$' | sort -n))

if [ ${#TIME_DIRS[@]} -eq 0 ]; then
    echo -e "${RED}❌ 没有找到时间步数据${NC}"
    exit 1
fi

# 检查eps数据
EPS_COUNT=0
for time in "${TIME_DIRS[@]}"; do
    [ -f "$time/eps" ] && ((EPS_COUNT++))
done

echo -e "${GREEN}   找到 ${#TIME_DIRS[@]} 个时间步: ${TIME_DIRS[0]} → ${TIME_DIRS[-1]}${NC}"
echo -e "${GREEN}   有eps数据: $EPS_COUNT 个时间步${NC}"

if [ $EPS_COUNT -eq 0 ]; then
    echo -e "${RED}❌ 没有找到eps数据${NC}"
    exit 1
fi

echo -e "${YELLOW}⚙️  步骤3: 创建渗透率计算配置...${NC}"
mkdir -p system

cat > system/expressions << EOF
expressions
{
    type            expressions;
    libs            ("libutilityFunctionObjects.so");
    
    expressions
    (
        {
            name        K;
            expression  "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/$KF";
            dimensions  [0 2 0 0 0 0 0];
        }
        {
            name        Kinv;
            expression  "$KF*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)";
            dimensions  [0 -2 0 0 0 0 0];
        }
    );
    
    writeControl    writeTime;
}
EOF

echo -e "${GREEN}   ✅ 配置完成 (kf = $KF)${NC}"

echo -e "${YELLOW}🧮 步骤4: 计算渗透率字段...${NC}"
PROCESSED=0
FAILED=0

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ]; then
        if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
            echo -e "${GREEN}   ✅ 时间步 $time 已有渗透率字段${NC}"
            ((PROCESSED++))
            continue
        fi
        
        echo -e "${CYAN}   🔄 处理时间步 $time...${NC}"
        
        if postProcess -func expressions -time "$time" > /dev/null 2>&1; then
            if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
                ((PROCESSED++))
                echo -e "${GREEN}   ✅ 时间步 $time 成功${NC}"
            else
                ((FAILED++))
                echo -e "${RED}   ❌ 时间步 $time 未生成字段${NC}"
            fi
        else
            ((FAILED++))
            echo -e "${RED}   ❌ 时间步 $time 失败${NC}"
        fi
    fi
done

echo -e "${BLUE}📊 渗透率计算结果: ${GREEN}$PROCESSED 成功${NC}, ${RED}$FAILED 失败${NC}"

echo -e "${YELLOW}📈 步骤5: 分析总渗透率...${NC}"

# 创建分析报告
REPORT_FILE="PERMEABILITY_ANALYSIS.txt"
cat > "$REPORT_FILE" << EOF
reactiveTransportDBSFoam 渗透率分析报告
================================================

分析时间: $(date)
算例目录: $(pwd)
时间步数量: ${#TIME_DIRS[@]}
时间范围: ${TIME_DIRS[0]} - ${TIME_DIRS[-1]}
有eps数据: $EPS_COUNT 个时间步
有渗透率数据: $PROCESSED 个时间步

渗透率计算公式:
K = eps³/((1-eps)²×$KF)
基于Kozeny-Carman方程

各时间步渗透率分析:
=====================================
EOF

# 分析每个时间步的渗透率
echo -e "${CYAN}   分析各时间步渗透率分布...${NC}"

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/K" ]; then
        echo -e "${BLUE}   📊 分析时间步 $time...${NC}"
        
        # 检查是否为uniform字段
        if grep -q "uniform" "$time/K"; then
            K_VALUE=$(grep "internalField.*uniform" "$time/K" | awk '{print $3}' | sed 's/;//')
            echo "时间步 $time: K = $K_VALUE m² (uniform)" >> "$REPORT_FILE"
            echo -e "${GREEN}      K = $K_VALUE m² (uniform)${NC}"
        else
            echo "时间步 $time: K = 非uniform字段 (需要详细分析)" >> "$REPORT_FILE"
            echo -e "${YELLOW}      K = 非uniform字段${NC}"
        fi
        
        # 分析eps值
        if [ -f "$time/eps" ] && grep -q "uniform" "$time/eps"; then
            EPS_VALUE=$(grep "internalField.*uniform" "$time/eps" | awk '{print $3}' | sed 's/;//')
            echo "         eps = $EPS_VALUE" >> "$REPORT_FILE"
            echo -e "${GREEN}      eps = $EPS_VALUE${NC}"
        fi
        
        echo "" >> "$REPORT_FILE"
    fi
done

# 添加总结
cat >> "$REPORT_FILE" << EOF

总渗透率演化分析:
=================
EOF

# 计算渗透率变化
FIRST_TIME=""
LAST_TIME=""
FIRST_K=""
LAST_K=""

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/K" ] && grep -q "uniform" "$time/K"; then
        K_VALUE=$(grep "internalField.*uniform" "$time/K" | awk '{print $3}' | sed 's/;//')
        
        if [ -z "$FIRST_TIME" ]; then
            FIRST_TIME=$time
            FIRST_K=$K_VALUE
        fi
        LAST_TIME=$time
        LAST_K=$K_VALUE
    fi
done

if [ -n "$FIRST_K" ] && [ -n "$LAST_K" ]; then
    # 使用bc计算变化倍数
    if command -v bc &> /dev/null; then
        CHANGE_RATIO=$(echo "scale=6; $LAST_K / $FIRST_K" | bc)
        cat >> "$REPORT_FILE" << EOF
初始渗透率 (时间步 $FIRST_TIME): $FIRST_K m²
最终渗透率 (时间步 $LAST_TIME): $LAST_K m²
渗透率变化倍数: $CHANGE_RATIO

EOF
        echo -e "${BLUE}📈 渗透率演化:${NC}"
        echo -e "${GREEN}   初始: $FIRST_K m² (时间步 $FIRST_TIME)${NC}"
        echo -e "${GREEN}   最终: $LAST_K m² (时间步 $LAST_TIME)${NC}"
        echo -e "${GREEN}   变化: ${CHANGE_RATIO}倍${NC}"
    fi
fi

cat >> "$REPORT_FILE" << EOF
ParaView可视化:
===============
1. VTK文件 (推荐): paraview VTK/*.vtm.series
2. OpenFOAM文件: paraview permeability_analysis.foam

可用字段:
- eps: 孔隙度 (所有时间步)
- K: 渗透率 ($PROCESSED 个时间步)
- Kinv: 渗透率倒数 ($PROCESSED 个时间步)
- 其他原有字段

使用建议:
1. 在ParaView中播放动画观看孔隙度演化
2. 查看渗透率的空间分布和时间变化
3. 对比不同时间步的渗透率分布
4. 分析孔隙度-渗透率关系
EOF

echo -e "${YELLOW}🎨 步骤6: 生成ParaView文件...${NC}"

# 清理旧文件
rm -f *.foam *.OpenFOAM
rm -rf VTK/

# 创建新的.foam文件
touch permeability_analysis.foam
echo -e "${GREEN}   ✅ 创建 permeability_analysis.foam${NC}"

# 转换为VTK格式
echo -e "${CYAN}   🔄 转换为VTK格式...${NC}"
foamToVTK > vtk_convert.log 2>&1

if [ -d "VTK" ]; then
    echo -e "${GREEN}   ✅ VTK文件生成成功${NC}"
    VTK_SUCCESS=true
else
    echo -e "${YELLOW}   ⚠️  VTK转换可能有问题${NC}"
    VTK_SUCCESS=false
fi

echo -e "${YELLOW}📝 步骤7: 生成分析报告...${NC}"
echo -e "${GREEN}   ✅ 报告保存到 $REPORT_FILE${NC}"

# 最终报告
echo ""
echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║                     后处理完成!                             ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"

echo -e "${GREEN}🎉 SUCCESS! 渗透率后处理完成${NC}"
echo ""
echo -e "${BLUE}📊 处理结果:${NC}"
echo -e "   时间步总数: ${#TIME_DIRS[@]}"
echo -e "   有eps数据: $EPS_COUNT"
echo -e "   有渗透率数据: $PROCESSED"
echo ""
echo -e "${BLUE}🚀 查看结果:${NC}"
if [ "$VTK_SUCCESS" = true ]; then
    echo -e "${GREEN}   paraview $(pwd)/VTK/*.vtm.series${NC}"
fi
echo -e "   paraview $(pwd)/permeability_analysis.foam"
echo -e "   cat $(pwd)/$REPORT_FILE"
echo ""
echo -e "${BLUE}📈 您现在可以:${NC}"
echo -e "   ✅ 在ParaView中观看完整的渗透率演化"
echo -e "   ✅ 分析每个时间步的渗透率分布"
echo -e "   ✅ 查看总渗透率变化趋势"
echo -e "   ✅ 研究孔隙度-渗透率关系"
echo ""
echo -e "${CYAN}完成时间: $(date)${NC}"
