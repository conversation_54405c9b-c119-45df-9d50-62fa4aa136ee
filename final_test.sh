#!/bin/bash

echo "🔬 Final Permeability Test for ParaView"
echo "======================================="

# Find latest time
latest_time=$(find . -maxdepth 1 -name "[0-9]*" -type d | sort -n | tail -1)
echo "📅 Latest time: $latest_time"

if [ -z "$latest_time" ]; then
    echo "❌ No time directories found!"
    exit 1
fi

# Check fields
echo ""
echo "📊 Checking fields in $latest_time:"
ls -la "$latest_time/" | grep -E "(K|Kinv|eps)" | while read line; do
    echo "   $line"
done

# Verify field contents
echo ""
echo "🔍 Verifying field headers:"

if [ -f "$latest_time/K" ]; then
    echo "   K field header:"
    head -20 "$latest_time/K" | grep -E "(dimensions|class|object)"
else
    echo "   ❌ K field not found"
fi

if [ -f "$latest_time/Kinv" ]; then
    echo "   Kinv field header:"
    head -20 "$latest_time/Kinv" | grep -E "(dimensions|class|object)"
else
    echo "   ❌ Kinv field not found"
fi

# Test VTK conversion
echo ""
echo "🔄 Testing VTK conversion:"
foamToVTK -latestTime > vtk_test.log 2>&1
if [ $? -eq 0 ]; then
    echo "   ✅ VTK conversion successful"
    grep "volScalar" vtk_test.log | head -1
else
    echo "   ❌ VTK conversion failed"
fi

# Create ParaView files
echo ""
echo "📁 Creating ParaView files:"
rm -f *.foam *.OpenFOAM
touch permeability_test.foam
echo "   ✅ Created permeability_test.foam"

# Final instructions
echo ""
echo "🎯 FINAL INSTRUCTIONS FOR PARAVIEW:"
echo "===================================="
echo ""
echo "Method 1 (VTK - RECOMMENDED):"
echo "   paraview VTK/*.vtm.series"
echo ""
echo "Method 2 (OpenFOAM):"
echo "   paraview permeability_test.foam"
echo ""
echo "In ParaView:"
echo "   1. Click Apply"
echo "   2. Look for K and Kinv in the field list"
echo "   3. If not visible, try:"
echo "      - Refresh/Reload"
echo "      - Check 'Volume Fields' section"
echo "      - Use VTK files instead"
echo ""
echo "✅ Test completed!"
