#!/usr/bin/env python3
"""
Simple verification script for permeability fields
"""

import os

def verify_permeability():
    print("🔍 Verifying Permeability Fields in reactiveTransportDBSFoam")
    print("=" * 60)
    
    # Find time directories
    time_dirs = []
    for item in os.listdir("."):
        try:
            float(item)
            time_dirs.append(item)
        except ValueError:
            continue
    
    time_dirs.sort(key=float)
    
    if not time_dirs:
        print("❌ No time directories found!")
        return False
    
    print(f"📁 Found {len(time_dirs)} time steps: {', '.join(time_dirs)}")
    
    # Check latest time step
    latest_time = time_dirs[-1]
    latest_dir = latest_time
    
    if not os.path.exists(latest_dir):
        print(f"❌ Directory {latest_dir} not found!")
        return False
    
    fields = os.listdir(latest_dir)
    
    print(f"\n📊 Fields in latest time step ({latest_time}):")
    for field in sorted(fields):
        if not field.startswith('.'):
            size = os.path.getsize(os.path.join(latest_dir, field))
            print(f"   📄 {field:<15} ({size:,} bytes)")
    
    # Check for permeability fields
    has_K = 'K' in fields
    has_Kinv = 'Kinv' in fields
    has_eps = 'eps' in fields
    
    print(f"\n🎯 Permeability Fields Status:")
    print(f"   {'✅' if has_K else '❌'} K (permeability)")
    print(f"   {'✅' if has_Kinv else '❌'} Kinv (inverse permeability)")
    print(f"   {'✅' if has_eps else '❌'} eps (porosity)")
    
    if has_K and has_Kinv:
        print(f"\n🎉 SUCCESS! Permeability fields are available!")
        print(f"   📈 You can now visualize permeability in ParaView")
        print(f"   🔧 Modified reactiveTransportDBSFoam solver is working correctly")
        
        # Check file sizes
        k_size = os.path.getsize(os.path.join(latest_dir, 'K'))
        kinv_size = os.path.getsize(os.path.join(latest_dir, 'Kinv'))
        print(f"\n📏 Field sizes:")
        print(f"   K: {k_size:,} bytes")
        print(f"   Kinv: {kinv_size:,} bytes")
        
        return True
    else:
        print(f"\n❌ WARNING: Permeability fields missing!")
        print(f"   Make sure you're using the modified reactiveTransportDBSFoam solver")
        return False

if __name__ == "__main__":
    verify_permeability()
