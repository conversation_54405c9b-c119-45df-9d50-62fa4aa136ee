#!/usr/bin/env python3
"""
直接创建渗透率字段文件，确保ParaView可以读取
"""

import os
import numpy as np

def create_permeability_fields(case_dir):
    """
    在指定的算例目录中创建渗透率字段
    """
    print(f"🔧 在 {case_dir} 中创建渗透率字段")
    
    # 找到最新的时间步
    time_dirs = []
    for item in os.listdir(case_dir):
        if os.path.isdir(os.path.join(case_dir, item)):
            try:
                float(item)
                time_dirs.append(item)
            except ValueError:
                continue
    
    if not time_dirs:
        print("❌ 没有找到时间步目录")
        return False
    
    time_dirs.sort(key=float)
    latest_time = time_dirs[-1]
    time_path = os.path.join(case_dir, latest_time)
    
    print(f"📅 最新时间步: {latest_time}")
    
    # 检查是否有eps字段
    eps_file = os.path.join(time_path, "eps")
    if not os.path.exists(eps_file):
        print("❌ 没有找到eps字段文件")
        return False
    
    print("📖 读取eps字段...")
    
    # 读取eps字段的头部信息
    with open(eps_file, 'r') as f:
        lines = f.readlines()
    
    # 找到数据开始位置
    data_start = 0
    dimensions_line = ""
    for i, line in enumerate(lines):
        if line.startswith("dimensions"):
            dimensions_line = line
        if line.strip() == "internalField   nonuniform List<scalar>":
            data_start = i + 2  # 跳过数量行
            break
        elif line.strip().startswith("internalField   uniform"):
            # 如果是uniform字段，需要特殊处理
            uniform_value = float(line.split()[-1].rstrip(';'))
            print(f"📊 eps是uniform字段，值为: {uniform_value}")
            create_uniform_permeability_fields(case_dir, latest_time, uniform_value)
            return True
    
    if data_start == 0:
        print("❌ 无法解析eps字段格式")
        return False
    
    # 读取数据数量
    num_cells = int(lines[data_start - 1].strip())
    print(f"📊 网格单元数: {num_cells}")
    
    # 读取eps数据
    eps_data = []
    for i in range(data_start, data_start + num_cells):
        if i < len(lines):
            eps_data.append(float(lines[i].strip()))
    
    if len(eps_data) != num_cells:
        print(f"❌ 数据读取不完整: {len(eps_data)}/{num_cells}")
        return False
    
    eps_array = np.array(eps_data)
    print(f"📈 eps范围: {eps_array.min():.6f} - {eps_array.max():.6f}")
    
    # 计算渗透率 (使用Kozeny-Carman方程)
    kf = 1.8e12  # Kozeny-Carman常数
    
    # 避免除零错误
    eps_safe = np.maximum(eps_array, 1e-6)
    eps_safe = np.minimum(eps_safe, 0.999999)
    
    # K = eps^3 / ((1-eps)^2 * kf)
    K_data = (eps_safe**3) / ((1 - eps_safe)**2 * kf)
    
    # Kinv = kf * (1-eps)^2 / eps^3
    Kinv_data = kf * ((1 - eps_safe)**2) / (eps_safe**3)
    
    print(f"📈 K范围: {K_data.min():.2e} - {K_data.max():.2e}")
    print(f"📈 Kinv范围: {Kinv_data.min():.2e} - {Kinv_data.max():.2e}")
    
    # 创建K字段文件
    create_field_file(time_path, "K", K_data, "[0 2 0 0 0 0 0]", lines[:data_start-2])
    
    # 创建Kinv字段文件
    create_field_file(time_path, "Kinv", Kinv_data, "[0 -2 0 0 0 0 0]", lines[:data_start-2])
    
    print("✅ 渗透率字段创建成功!")
    return True

def create_uniform_permeability_fields(case_dir, time_dir, eps_value):
    """
    为uniform eps字段创建uniform渗透率字段
    """
    time_path = os.path.join(case_dir, time_dir)
    
    kf = 1.8e12
    eps_safe = max(min(eps_value, 0.999999), 1e-6)
    
    K_value = (eps_safe**3) / ((1 - eps_safe)**2 * kf)
    Kinv_value = kf * ((1 - eps_safe)**2) / (eps_safe**3)
    
    # 创建uniform K字段
    k_content = f"""/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  2206                                  |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "{time_dir}";
    object      K;
}}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 0 0 0 0 0];

internalField   uniform {K_value:.12e};

boundaryField
{{
    #includeEtc "caseDicts/setConstraintTypes"
}}

// ************************************************************************* //
"""
    
    # 创建uniform Kinv字段
    kinv_content = f"""/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  2206                                  |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "{time_dir}";
    object      Kinv;
}}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 -2 0 0 0 0 0];

internalField   uniform {Kinv_value:.12e};

boundaryField
{{
    #includeEtc "caseDicts/setConstraintTypes"
}}

// ************************************************************************* //
"""
    
    with open(os.path.join(time_path, "K"), 'w') as f:
        f.write(k_content)
    
    with open(os.path.join(time_path, "Kinv"), 'w') as f:
        f.write(kinv_content)
    
    print(f"✅ 创建uniform渗透率字段: K={K_value:.2e}, Kinv={Kinv_value:.2e}")

def create_field_file(time_path, field_name, data, dimensions, header_lines):
    """
    创建字段文件
    """
    file_path = os.path.join(time_path, field_name)
    
    with open(file_path, 'w') as f:
        # 写入头部
        for line in header_lines:
            if "object" in line:
                f.write(f'    object      {field_name};\n')
            else:
                f.write(line)
        
        # 写入量纲
        f.write(f"dimensions      {dimensions};\n\n")
        
        # 写入数据
        f.write("internalField   nonuniform List<scalar>\n")
        f.write(f"{len(data)}\n(\n")
        
        for value in data:
            f.write(f"{value:.12e}\n")
        
        f.write(");\n\n")
        f.write("boundaryField\n{\n")
        f.write("    #includeEtc \"caseDicts/setConstraintTypes\"\n")
        f.write("}\n\n")
        f.write("// ************************************************************************* //\n")
    
    print(f"✅ 创建字段文件: {file_path}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        case_dir = sys.argv[1]
    else:
        case_dir = "."
    
    create_permeability_fields(case_dir)
