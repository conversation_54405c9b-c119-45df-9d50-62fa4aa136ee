#!/bin/bash

# 一键解决reactiveTransportDBSFoam渗透率输出问题
# 用法: ./fix_permeability_final.sh [算例目录]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 reactiveTransportDBSFoam 渗透率输出一键修复${NC}"
echo "======================================================="

# 设置算例目录
if [ -z "$1" ]; then
    CASE_DIR="."
else
    CASE_DIR="$1"
fi

echo -e "${YELLOW}📁 算例目录: $CASE_DIR${NC}"

# 检查算例目录
if [ ! -d "$CASE_DIR" ]; then
    echo -e "${RED}❌ 算例目录不存在: $CASE_DIR${NC}"
    exit 1
fi

if [ ! -d "$CASE_DIR/system" ] || [ ! -d "$CASE_DIR/constant" ]; then
    echo -e "${RED}❌ 不是有效的OpenFOAM算例目录${NC}"
    exit 1
fi

cd "$CASE_DIR"

# 找到最新时间步
echo -e "${YELLOW}🔍 查找时间步数据...${NC}"
LATEST_TIME=$(find . -maxdepth 1 -name "[0-9]*" -type d | sort -n | tail -1 | sed 's|./||')

if [ -z "$LATEST_TIME" ]; then
    echo -e "${RED}❌ 没有找到时间步数据${NC}"
    echo -e "${YELLOW}💡 请先运行求解器生成数据${NC}"
    exit 1
fi

echo -e "${GREEN}📅 最新时间步: $LATEST_TIME${NC}"

# 检查eps字段
if [ ! -f "$LATEST_TIME/eps" ]; then
    echo -e "${RED}❌ 没有找到eps字段文件${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 找到eps字段${NC}"

# 方法1: 使用postProcess expressions
echo -e "${YELLOW}🔧 方法1: 使用postProcess创建渗透率字段...${NC}"

# 创建expressions字典
mkdir -p system
cat > system/expressions << 'EOF'
expressions
{
    type            expressions;
    libs            ("libutilityFunctionObjects.so");
    
    expressions
    (
        {
            name        K;
            expression  "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/1.8e12";
            dimensions  [0 2 0 0 0 0 0];
        }
        {
            name        Kinv;
            expression  "1.8e12*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)";
            dimensions  [0 -2 0 0 0 0 0];
        }
    );
    
    writeControl    writeTime;
}
EOF

# 运行postProcess
if postProcess -func expressions -time "$LATEST_TIME" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ postProcess方法成功${NC}"
else
    echo -e "${YELLOW}⚠️  postProcess方法失败，尝试其他方法...${NC}"
    
    # 方法2: 使用funkySetFields (如果可用)
    if command -v funkySetFields &> /dev/null; then
        echo -e "${YELLOW}🔧 方法2: 使用funkySetFields...${NC}"
        
        funkySetFields -time "$LATEST_TIME" -field K -expression "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/1.8e12" -create > /dev/null 2>&1 || true
        funkySetFields -time "$LATEST_TIME" -field Kinv -expression "1.8e12*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)" -create > /dev/null 2>&1 || true
        
        if [ -f "$LATEST_TIME/K" ] && [ -f "$LATEST_TIME/Kinv" ]; then
            echo -e "${GREEN}✅ funkySetFields方法成功${NC}"
        else
            echo -e "${YELLOW}⚠️  funkySetFields方法失败${NC}"
        fi
    fi
fi

# 检查结果
if [ -f "$LATEST_TIME/K" ] && [ -f "$LATEST_TIME/Kinv" ]; then
    echo -e "${GREEN}🎉 渗透率字段创建成功!${NC}"
    
    # 显示字段信息
    K_SIZE=$(stat -c%s "$LATEST_TIME/K" 2>/dev/null || echo "unknown")
    KINV_SIZE=$(stat -c%s "$LATEST_TIME/Kinv" 2>/dev/null || echo "unknown")
    
    echo -e "${BLUE}📊 字段信息:${NC}"
    echo -e "   K (渗透率): ${K_SIZE} bytes"
    echo -e "   Kinv (渗透率倒数): ${KINV_SIZE} bytes"
    
else
    echo -e "${RED}❌ 渗透率字段创建失败${NC}"
    echo -e "${YELLOW}💡 尝试手动方法...${NC}"
    
    # 方法3: 创建简单的uniform字段
    EPS_VALUE=$(grep "internalField.*uniform" "$LATEST_TIME/eps" | awk '{print $3}' | sed 's/;//' || echo "0.5")
    
    if [[ "$EPS_VALUE" =~ ^[0-9]*\.?[0-9]+$ ]]; then
        echo -e "${YELLOW}🔧 创建uniform渗透率字段 (eps=$EPS_VALUE)...${NC}"
        
        # 计算渗透率值
        K_VALUE=$(python3 -c "import math; eps=max(min(float('$EPS_VALUE'), 0.999999), 1e-6); print(f'{eps**3/((1-eps)**2*1.8e12):.12e}')")
        KINV_VALUE=$(python3 -c "import math; eps=max(min(float('$EPS_VALUE'), 0.999999), 1e-6); print(f'{1.8e12*(1-eps)**2/eps**3:.12e}')")
        
        # 创建K字段
        cat > "$LATEST_TIME/K" << EOF
/*--------------------------------*- C++ -*----------------------------------*\\
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "$LATEST_TIME";
    object      K;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 0 0 0 0 0];

internalField   uniform $K_VALUE;

boundaryField
{
    #includeEtc "caseDicts/setConstraintTypes"
}

// ************************************************************************* //
EOF

        # 创建Kinv字段
        cat > "$LATEST_TIME/Kinv" << EOF
/*--------------------------------*- C++ -*----------------------------------*\\
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "$LATEST_TIME";
    object      Kinv;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 -2 0 0 0 0 0];

internalField   uniform $KINV_VALUE;

boundaryField
{
    #includeEtc "caseDicts/setConstraintTypes"
}

// ************************************************************************* //
EOF
        
        echo -e "${GREEN}✅ 手动创建uniform字段成功${NC}"
        echo -e "   K = $K_VALUE"
        echo -e "   Kinv = $KINV_VALUE"
    fi
fi

# 最终检查
if [ -f "$LATEST_TIME/K" ] && [ -f "$LATEST_TIME/Kinv" ]; then
    echo ""
    echo -e "${GREEN}🎉 SUCCESS! 渗透率字段已创建${NC}"
    
    # 创建ParaView文件
    echo -e "${YELLOW}📄 创建ParaView文件...${NC}"
    rm -f *.foam *.OpenFOAM
    touch permeability.foam
    
    # 转换为VTK
    echo -e "${YELLOW}🔄 转换为VTK格式...${NC}"
    foamToVTK -latestTime > /dev/null 2>&1 || true
    
    echo ""
    echo -e "${BLUE}🚀 在ParaView中查看:${NC}"
    echo -e "${GREEN}   推荐: paraview VTK/*.vtm.series${NC}"
    echo -e "   备选: paraview permeability.foam"
    echo ""
    echo -e "${BLUE}📊 可用字段:${NC}"
    echo -e "   ✅ K (渗透率)"
    echo -e "   ✅ Kinv (渗透率倒数)"
    echo -e "   ✅ eps (孔隙度)"
    echo ""
    echo -e "${GREEN}🎯 问题已解决!${NC}"
    
else
    echo ""
    echo -e "${RED}❌ 所有方法都失败了${NC}"
    echo -e "${YELLOW}💡 请检查:${NC}"
    echo -e "   1. OpenFOAM环境是否正确加载"
    echo -e "   2. 是否有写入权限"
    echo -e "   3. eps字段是否有效"
    exit 1
fi
