#!/bin/bash

# 自动渗透率处理和可视化脚本
# 用法: ./auto_permeability.sh [算例目录]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示标题
echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                   自动渗透率处理系统                         ║"
echo "║              reactiveTransportDBSFoam 专用                   ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 设置算例目录
if [ -z "$1" ]; then
    echo -e "${YELLOW}请输入算例目录路径 (或按Enter使用当前目录):${NC}"
    read -r CASE_DIR
    if [ -z "$CASE_DIR" ]; then
        CASE_DIR="."
    fi
else
    CASE_DIR="$1"
fi

echo -e "${BLUE}📁 目标算例: $CASE_DIR${NC}"

# 检查算例目录
if [ ! -d "$CASE_DIR" ]; then
    echo -e "${RED}❌ 算例目录不存在: $CASE_DIR${NC}"
    exit 1
fi

if [ ! -d "$CASE_DIR/system" ] || [ ! -d "$CASE_DIR/constant" ]; then
    echo -e "${RED}❌ 不是有效的OpenFOAM算例目录${NC}"
    exit 1
fi

cd "$CASE_DIR"

echo -e "${YELLOW}🚀 开始自动处理...${NC}"

# 步骤1: 检查并重构并行数据
echo -e "${BLUE}📦 步骤1: 处理并行数据...${NC}"

if [ -d "processor0" ]; then
    echo -e "${YELLOW}   发现并行数据，开始重构...${NC}"
    
    # 检查是否已经重构过
    if [ ! -d "0" ] && [ ! -d "800" ]; then
        echo -e "${CYAN}   正在重构并行数据...${NC}"
        reconstructPar > reconstruct.log 2>&1
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}   ✅ 并行数据重构成功${NC}"
        else
            echo -e "${YELLOW}   ⚠️  重构失败，尝试继续...${NC}"
        fi
    else
        echo -e "${GREEN}   ✅ 数据已经重构${NC}"
    fi
else
    echo -e "${GREEN}   ✅ 使用串行数据${NC}"
fi

# 步骤2: 查找时间步
echo -e "${BLUE}📊 步骤2: 分析时间步数据...${NC}"

TIME_DIRS=($(find . -maxdepth 1 -name "[0-9]*" -type d | sed 's|./||' | grep -E '^[0-9]+(\.[0-9]+)?$' | sort -n))

if [ ${#TIME_DIRS[@]} -eq 0 ]; then
    echo -e "${RED}❌ 没有找到时间步数据${NC}"
    echo -e "${YELLOW}💡 请确保算例已经运行完成${NC}"
    exit 1
fi

echo -e "${GREEN}   找到 ${#TIME_DIRS[@]} 个时间步: ${TIME_DIRS[0]} → ${TIME_DIRS[-1]}${NC}"

# 检查eps数据
EPS_COUNT=0
for time in "${TIME_DIRS[@]}"; do
    [ -f "$time/eps" ] && ((EPS_COUNT++))
done

echo -e "${GREEN}   有eps数据的时间步: $EPS_COUNT${NC}"

# 步骤3: 创建渗透率计算配置
echo -e "${BLUE}🔧 步骤3: 配置渗透率计算...${NC}"

mkdir -p system

cat > system/expressions << 'EOF'
expressions
{
    type            expressions;
    libs            ("libutilityFunctionObjects.so");
    
    expressions
    (
        {
            name        K;
            expression  "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/1.8e12";
            dimensions  [0 2 0 0 0 0 0];
        }
        {
            name        Kinv;
            expression  "1.8e12*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)";
            dimensions  [0 -2 0 0 0 0 0];
        }
    );
    
    writeControl    writeTime;
}
EOF

echo -e "${GREEN}   ✅ 配置文件创建完成 (Kozeny-Carman方程, kf=1.8e12)${NC}"

# 步骤4: 批量计算渗透率
echo -e "${BLUE}⚙️  步骤4: 计算渗透率字段...${NC}"

PROCESSED=0
SKIPPED=0
FAILED=0

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ]; then
        # 检查是否已有渗透率字段
        if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
            echo -e "${GREEN}   ✅ 时间步 $time 已有渗透率字段${NC}"
            ((PROCESSED++))
            ((SKIPPED++))
            continue
        fi
        
        echo -e "${CYAN}   🔄 处理时间步 $time...${NC}"
        
        if postProcess -func expressions -time "$time" > /dev/null 2>&1; then
            if [ -f "$time/K" ] && [ -f "$time/Kinv" ]; then
                ((PROCESSED++))
                echo -e "${GREEN}   ✅ 时间步 $time 成功${NC}"
            else
                echo -e "${YELLOW}   ⚠️  时间步 $time 未生成字段${NC}"
                ((FAILED++))
            fi
        else
            echo -e "${RED}   ❌ 时间步 $time 失败${NC}"
            ((FAILED++))
        fi
    else
        echo -e "${YELLOW}   ⚠️  时间步 $time 无eps数据${NC}"
    fi
done

echo -e "${BLUE}📊 渗透率计算结果: ${GREEN}$PROCESSED成功${NC} (${YELLOW}$SKIPPED已存在${NC}) ${RED}$FAILED失败${NC}"

# 步骤5: 生成ParaView文件
echo -e "${BLUE}🎨 步骤5: 生成ParaView文件...${NC}"

# 清理旧文件
rm -f *.foam *.OpenFOAM
rm -rf VTK/

# 创建新的.foam文件
FOAM_NAME="permeability_$(date +%Y%m%d_%H%M%S).foam"
touch "$FOAM_NAME"
echo -e "${GREEN}   ✅ 创建 $FOAM_NAME${NC}"

# 转换为VTK格式
echo -e "${CYAN}   🔄 转换为VTK格式...${NC}"
foamToVTK > vtk_convert.log 2>&1 &
VTK_PID=$!

# 显示进度
echo -e "${YELLOW}   转换中..."
while kill -0 $VTK_PID 2>/dev/null; do
    echo -ne "${YELLOW}   ⏳ VTK转换进行中...${NC}\r"
    sleep 2
done
wait $VTK_PID

if [ -d "VTK" ]; then
    echo -e "${GREEN}   ✅ VTK文件生成成功${NC}"
    VTK_SUCCESS=true
else
    echo -e "${YELLOW}   ⚠️  VTK转换可能有问题${NC}"
    VTK_SUCCESS=false
fi

# 步骤6: 验证结果
echo -e "${BLUE}🔍 步骤6: 验证结果...${NC}"

TOTAL_WITH_K=0
TOTAL_WITH_KINV=0
SAMPLE_TIME=""

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ]; then
        [ -f "$time/K" ] && ((TOTAL_WITH_K++))
        [ -f "$time/Kinv" ] && ((TOTAL_WITH_KINV++))
        [ -z "$SAMPLE_TIME" ] && [ -f "$time/K" ] && SAMPLE_TIME="$time"
    fi
done

echo -e "${BLUE}📈 最终统计:${NC}"
echo -e "   时间步总数: ${#TIME_DIRS[@]}"
echo -e "   有eps数据: $EPS_COUNT"
echo -e "   有K字段: $TOTAL_WITH_K"
echo -e "   有Kinv字段: $TOTAL_WITH_KINV"

# 显示样本数据
if [ -n "$SAMPLE_TIME" ]; then
    echo -e "${BLUE}📋 样本数据 (时间步 $SAMPLE_TIME):${NC}"
    
    if grep -q "uniform" "$SAMPLE_TIME/K" 2>/dev/null; then
        K_VALUE=$(grep "internalField.*uniform" "$SAMPLE_TIME/K" | awk '{print $3}' | sed 's/;//')
        echo -e "   K值: $K_VALUE m²"
    fi
    
    if grep -q "uniform" "$SAMPLE_TIME/eps" 2>/dev/null; then
        EPS_VALUE=$(grep "internalField.*uniform" "$SAMPLE_TIME/eps" | awk '{print $3}' | sed 's/;//')
        echo -e "   eps值: $EPS_VALUE"
    fi
fi

# 步骤7: 生成使用说明
echo -e "${BLUE}📝 步骤7: 生成使用说明...${NC}"

cat > PARAVIEW_USAGE.txt << EOF
ParaView渗透率可视化使用说明
============================

生成时间: $(date)
算例目录: $(pwd)
时间步范围: ${TIME_DIRS[0]} - ${TIME_DIRS[-1]}

1. 打开ParaView:
   方法1 (VTK推荐): paraview VTK/*.vtm.series
   方法2 (OpenFOAM): paraview $FOAM_NAME

2. 可用字段:
   - eps: 孔隙度 (所有时间步)
   - K: 渗透率 ($TOTAL_WITH_K个时间步)
   - Kinv: 渗透率倒数 ($TOTAL_WITH_KINV个时间步)
   - C: 浓度
   - U: 速度
   - p: 压力

3. 操作步骤:
   a) 点击Apply加载数据
   b) 选择要显示的字段
   c) 使用时间控制器查看不同时间步
   d) 创建等值面、切片等可视化

4. 分析建议:
   - 观看eps随时间的变化动画
   - 分析最终时间步的渗透率分布
   - 对比孔隙度和渗透率的关系

5. 渗透率计算公式:
   K = eps³/((1-eps)²×1.8e12)
   基于Kozeny-Carman方程
EOF

echo -e "${GREEN}   ✅ 使用说明已保存到 PARAVIEW_USAGE.txt${NC}"

# 最终报告
echo ""
echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║                        处理完成!                            ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"

if [ "$TOTAL_WITH_K" -gt 0 ]; then
    echo -e "${GREEN}🎉 SUCCESS! 渗透率字段处理完成${NC}"
    
    echo ""
    echo -e "${BLUE}🚀 立即使用:${NC}"
    if [ "$VTK_SUCCESS" = true ]; then
        echo -e "${GREEN}   paraview $(pwd)/VTK/*.vtm.series${NC}"
    fi
    echo -e "   paraview $(pwd)/$FOAM_NAME"
    
    echo ""
    echo -e "${BLUE}📊 您现在可以:${NC}"
    echo -e "   ✅ 观看孔隙度演化动画"
    echo -e "   ✅ 分析渗透率空间分布"
    echo -e "   ✅ 研究孔隙度-渗透率关系"
    echo -e "   ✅ 创建专业科研图表"
    
    echo ""
    echo -e "${YELLOW}💡 提示: 查看 PARAVIEW_USAGE.txt 获取详细使用说明${NC}"
    
else
    echo -e "${YELLOW}⚠️  渗透率字段生成不完整${NC}"
    echo -e "${YELLOW}💡 但孔隙度数据仍可用于分析${NC}"
fi

echo ""
echo -e "${CYAN}处理完成时间: $(date)${NC}"
