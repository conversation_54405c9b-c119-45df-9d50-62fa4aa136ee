#!/bin/bash

echo "🔧 Fixing ParaView visualization for permeability fields"
echo "======================================================="

# Step 1: Clean old ParaView files
echo "1. Cleaning old ParaView files..."
rm -f *.foam
rm -f *.OpenFOAM
rm -rf VTK/

# Step 2: Create fresh .foam file
echo "2. Creating fresh .foam file..."
touch permeability.foam

# Step 3: Convert to VTK format (most reliable for ParaView)
echo "3. Converting to VTK format..."
foamToVTK -latestTime

# Step 4: Check if K and Kinv fields are present
echo "4. Verifying permeability fields..."
latest_time=$(find . -maxdepth 1 -name "[0-9]*" -type d | sort -n | tail -1)
if [ -n "$latest_time" ]; then
    echo "   Latest time: $latest_time"
    if [ -f "$latest_time/K" ]; then
        echo "   ✅ K field found"
    else
        echo "   ❌ K field missing"
    fi
    if [ -f "$latest_time/Kinv" ]; then
        echo "   ✅ Kinv field found"
    else
        echo "   ❌ Kinv field missing"
    fi
else
    echo "   ❌ No time directories found"
fi

# Step 5: Create ParaView state file
echo "5. Creating ParaView instructions..."
cat > paraview_instructions.txt << EOF
ParaView Visualization Instructions for Permeability Fields
===========================================================

Method 1: Use VTK files (RECOMMENDED)
-------------------------------------
1. Open ParaView
2. File -> Open
3. Navigate to VTK/ folder
4. Select the .vtm.series file
5. Click Apply
6. In the Properties panel, you should see all fields including K and Kinv

Method 2: Use OpenFOAM reader
-----------------------------
1. Open ParaView
2. File -> Open
3. Select permeability.foam
4. Click Apply
5. In the Properties panel, expand "Volume Fields"
6. Check the boxes for:
   ✅ K (permeability)
   ✅ Kinv (inverse permeability)
   ✅ eps (porosity)
7. Click Apply again

Method 3: Force refresh
-----------------------
If fields don't appear:
1. Close ParaView completely
2. Delete ParaView cache: rm -rf ~/.config/ParaView*
3. Reopen ParaView and try again

Troubleshooting
---------------
- If K/Kinv still don't appear, the solver may need to run longer
- Check that you're using the modified reactiveTransportDBSFoam solver
- Verify fields exist: ls latest_time_folder/
EOF

echo ""
echo "✅ ParaView fix completed!"
echo ""
echo "📖 Next steps:"
echo "   1. Read paraview_instructions.txt for detailed instructions"
echo "   2. Use VTK files for most reliable visualization"
echo "   3. If problems persist, run the solver for one more time step"
echo ""
echo "🚀 To open ParaView:"
echo "   paraview VTK/*.vtm.series"
echo "   or"
echo "   paraview permeability.foam"
