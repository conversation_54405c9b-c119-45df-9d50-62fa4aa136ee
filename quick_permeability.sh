#!/bin/bash

# 快速渗透率处理脚本 - 一键运行
# 用法: ./quick_permeability.sh [算例目录]

echo "🚀 快速渗透率处理 - reactiveTransportDBSFoam"
echo "============================================="

# 设置算例目录
CASE_DIR="${1:-.}"

echo "📁 处理算例: $CASE_DIR"

# 检查目录
if [ ! -d "$CASE_DIR/system" ]; then
    echo "❌ 不是有效的OpenFOAM算例目录"
    exit 1
fi

cd "$CASE_DIR"

# 1. 重构并行数据
echo "🔄 重构数据..."
[ -d "processor0" ] && reconstructPar > /dev/null 2>&1

# 2. 创建配置
echo "⚙️  配置计算..."
mkdir -p system
cat > system/expressions << 'EOF'
expressions
{
    type            expressions;
    libs            ("libutilityFunctionObjects.so");
    expressions
    (
        {
            name        K;
            expression  "pow(max(eps,1e-6),3)/pow(1-min(max(eps,1e-6),0.999999),2)/1.8e12";
            dimensions  [0 2 0 0 0 0 0];
        }
        {
            name        Kinv;
            expression  "1.8e12*pow(1-min(max(eps,1e-6),0.999999),2)/pow(max(eps,1e-6),3)";
            dimensions  [0 -2 0 0 0 0 0];
        }
    );
    writeControl    writeTime;
}
EOF

# 3. 计算渗透率
echo "🧮 计算渗透率..."
TIME_DIRS=($(find . -maxdepth 1 -name "[0-9]*" -type d | sed 's|./||' | grep -E '^[0-9]+(\.[0-9]+)?$' | sort -n))

for time in "${TIME_DIRS[@]}"; do
    if [ -f "$time/eps" ] && [ ! -f "$time/K" ]; then
        postProcess -func expressions -time "$time" > /dev/null 2>&1
    fi
done

# 4. 生成ParaView文件
echo "🎨 生成ParaView文件..."
rm -f *.foam *.OpenFOAM
touch permeability_auto.foam
foamToVTK > /dev/null 2>&1

# 5. 统计结果
TOTAL_TIMES=${#TIME_DIRS[@]}
WITH_K=0
for time in "${TIME_DIRS[@]}"; do
    [ -f "$time/K" ] && ((WITH_K++))
done

echo ""
echo "✅ 处理完成!"
echo "📊 时间步: $TOTAL_TIMES, 有渗透率: $WITH_K"
echo ""
echo "🚀 打开ParaView:"
echo "   paraview $(pwd)/VTK/*.vtm.series"
echo "   或"
echo "   paraview $(pwd)/permeability_auto.foam"
echo ""
echo "📋 可用字段: eps(孔隙度), K(渗透率), Kinv(渗透率倒数), C, U, p"
