#!/bin/bash

# 添加渗透率字段到reactiveTransportDBSFoam算例
# 用法: ./add_permeability.sh [算例目录]

# 设置算例目录
if [ -z "$1" ]; then
    CASE_DIR="."
else
    CASE_DIR="$1"
fi

echo "🔧 添加渗透率字段到 $CASE_DIR"
echo "========================================"

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    echo "❌ 需要Python3"
    exit 1
fi

# 检查算例目录是否存在
if [ ! -d "$CASE_DIR" ]; then
    echo "❌ 算例目录不存在: $CASE_DIR"
    exit 1
fi

# 检查是否是OpenFOAM算例
if [ ! -d "$CASE_DIR/system" ] || [ ! -d "$CASE_DIR/constant" ]; then
    echo "❌ 不是有效的OpenFOAM算例目录: $CASE_DIR"
    exit 1
fi

# 运行Python脚本创建渗透率字段
python3 /home/<USER>/GeoChemFoam-5.1/create_permeability_fields.py "$CASE_DIR"

# 检查是否成功
if [ $? -ne 0 ]; then
    echo "❌ 渗透率字段创建失败"
    exit 1
fi

# 创建新的.foam文件
echo "📄 创建新的ParaView文件..."
rm -f "$CASE_DIR"/*.foam "$CASE_DIR"/*.OpenFOAM
touch "$CASE_DIR/permeability.foam"

# 转换为VTK格式
echo "🔄 转换为VTK格式..."
cd "$CASE_DIR" && foamToVTK -latestTime > /dev/null 2>&1

echo ""
echo "✅ 渗透率字段添加成功!"
echo ""
echo "🚀 在ParaView中查看:"
echo "   方法1 (推荐): paraview $CASE_DIR/VTK/*.vtm.series"
echo "   方法2: paraview $CASE_DIR/permeability.foam"
echo ""
echo "📊 可用字段:"
echo "   - K (渗透率)"
echo "   - Kinv (渗透率倒数)"
echo "   - eps (孔隙度)"
echo ""
echo "💡 如果在ParaView中看不到字段:"
echo "   1. 确保点击'Apply'按钮"
echo "   2. 检查'Volume Fields'部分"
echo "   3. 尝试使用VTK文件 (更可靠)"
echo ""
