#!/bin/bash

# ParaView自动启动脚本
# 用法: ./open_paraview.sh [算例目录]

CASE_DIR="${1:-.}"

echo "🎨 启动ParaView查看渗透率数据"
echo "算例目录: $CASE_DIR"

cd "$CASE_DIR"

# 检查VTK文件
if [ -d "VTK" ] && [ -f VTK/*.vtm.series ]; then
    echo "✅ 使用VTK文件 (推荐)"
    VTK_FILE=$(ls VTK/*.vtm.series | head -1)
    echo "🚀 启动: paraview $VTK_FILE"
    paraview "$VTK_FILE" &
    
elif [ -f *.foam ]; then
    echo "✅ 使用OpenFOAM文件"
    FOAM_FILE=$(ls *.foam | head -1)
    echo "🚀 启动: paraview $FOAM_FILE"
    paraview "$FOAM_FILE" &
    
else
    echo "❌ 没有找到ParaView文件"
    echo "💡 请先运行: ./quick_permeability.sh"
    exit 1
fi

echo ""
echo "📋 ParaView使用提示:"
echo "1. 点击 Apply 加载数据"
echo "2. 选择字段: eps(孔隙度), K(渗透率), Kinv"
echo "3. 使用时间控制器查看不同时间步"
echo "4. 创建等值面、切片等可视化"
